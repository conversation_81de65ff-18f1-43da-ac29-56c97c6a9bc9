/*
 * @Date: 2024-01-05 13:52:37
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-05 15:35:46
 * @FilePath: /yanxuan-rpa-common-server/web/src/services/codeService.ts
 * @Description: 验证码提交相关的接口
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import { axiosService } from '@sharkr/request'
import { AjaxResult } from '@shark/core'
import { CodeSubmitVO } from '@/interfaces/codeInterface'

/**
 * @description: 提交验证码接口
 * @param {CodeSubmitVO} params 提交验证码的参数
 * @returns {Promise<AjaxResult<boolean>>} 返回值
 */
export const submitCode = (params: CodeSubmitVO): Promise<boolean> =>
  new Promise((resolve, reject) => {
    axiosService
      .post('/xhr/code/submitCode', params)
      .then(res => {
        if (res.code === 200) {
          resolve(res.data)
        } else {
          reject(res)
        }
      })
      .catch(err => {
        reject(err)
      })
  })
