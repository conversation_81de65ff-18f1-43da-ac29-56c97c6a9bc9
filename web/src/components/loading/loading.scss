.rpa-component-loading {
  position: fixed;
  z-index: 50;

  .loading-mask {
    position: fixed;
    width: 100%;
    height: 100%;
    opacity: rgba($color: #ffffff, $alpha: 0);
    top: 0;
    left: 0;
  }

  .loading-container {
    position: fixed;
    width: 2.4rem;
    height: 1.68rem;
    background-color: rgba($color: #000000, $alpha: 0.7);
    border-radius: 0.05rem;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .loading-icon {
      margin: 0.32rem auto 0 auto;
      width: 0.48rem;
      height: 0.48rem;
      color: #ffffff;
      display: flex;
    }

    .loading-text {
      font-family: PingFang SC;
      font-size: 0.28rem;
      line-height: 0.4rem;
      text-align: center;
      margin: 0.16rem auto 0 auto;
      color: #ffffff;
    }
  }
}
