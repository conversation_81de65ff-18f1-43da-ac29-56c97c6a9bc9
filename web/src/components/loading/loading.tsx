/*
 * @Date: 2024-01-05 14:41:55
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-05 15:10:40
 * @FilePath: /yanxuan-rpa-common-server/web/src/components/loading/loading.tsx
 * @Description: 加载状态组件
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import React from 'react'
import { Loading3QuartersOutlined } from '@ant-design/icons'

import './loading.scss'

interface IProps {
  // 是否展示
  isShow: boolean
  // 加载中文案
  loadingText?: string
}

export const Loading: React.FC<IProps> = props => {
  const { isShow, loadingText = '加载中' } = props
  return !isShow ? null : (
    <div className="rpa-component-loading">
      <div className="loading-mask"></div>
      <div className="loading-container">
        <div className="loading-icon">
          <Loading3QuartersOutlined
            spin
            rev={null}
            style={{ fontSize: '0.48rem' }}
            translate={null}
          />
        </div>
        <div className="loading-text">{loadingText}</div>
      </div>
    </div>
  )
}
