/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-05 15:39:14
 * @FilePath: /yanxuan-rpa-common-server/web/src/utils/common.ts
 * @Description: 通用工具
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

// 获取地址栏参数
export const getQueryString = (name: string): string | null => {
  const reg = new RegExp(`([?|&])${name}=([^&]*)(&|$)`, 'i')
  const result = window.location.href.match(reg)
  if (result !== null) {
    return result[2]
  }
  return null
}
