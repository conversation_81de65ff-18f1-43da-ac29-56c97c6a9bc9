/*
 * @Date: 2024-01-05 10:52:00
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-04 10:24:11
 * @FilePath: /yanxuan-rpa-common-server/web/src/pages/codeInput/codeInput.tsx
 * @Description: 页面主文件
 * @Author: <PERSON>wolf<PERSON> <EMAIL>
 */

import React, { useEffect, useState } from 'react'
import { Toast } from 'antd-mobile'
import { submitCode } from '@/services/codeService'
import { CodeSubmitVO } from '@/interfaces/codeInterface'
import { Loading } from '@/components'
import './codeInput.scss'
import { getQueryString } from '@/utils'

/**
 * @description: 验证码填写页面主函数
 * @returns {React.FC} ReactNode
 */
export const CodeInput: React.FC = () => {
  const [code, setCode] = useState<string>('')
  const [showLoading, setShowLoading] = useState<boolean>(false)
  const [phone, setPhone] = useState<string>('')

  useEffect(() => {
    const phone = getQueryString('phone') || ''
    setPhone(phone)
  })

  /**
   * @description: 处理验证码输入事件
   * @param {React.ChangeEvent<HTMLInputElement>} e 输入事件
   * @returns {void}
   */
  const handleCodeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCode(e.target.value)
  }

  /**
   * @description: 计算提交按钮类名
   * @param {string} code 输入的验证码
   * @returns {string} 类名
   */
  const getSubmitButtonClassName = (code: string): string => {
    if (code.length > 0) {
      return 'code-submit-button'
    } else {
      return 'code-submit-button button-disabled'
    }
  }

  /**
   * @description: 提交验证码
   * @returns {void}
   */
  const handleSubmit = async () => {
    if (code.length < 1) {
      return
    }
    const key = getQueryString('key') || ''
    const params: CodeSubmitVO = {
      code: code,
      key
    }
    setShowLoading(true)
    await submitCode(params)
      .then(res => {
        setShowLoading(false)
        if (res) {
          Toast.show('提交成功')
        } else {
          Toast.show('提交失败')
        }
      })
      .catch(err => {
        setShowLoading(false)
        Toast.show('提交失败')
      })
  }

  return (
    <div className="code-input-page-container">
      <div className="code-input-page">
        <div className="page-header-container"></div>
        <div className="input-wrap">
          <div className="input-title">欢迎使用</div>
          <div className="input-sub-title">严选自动化执行机器人</div>
          <div className="input-phone-tips">{phone}</div>
          <div className="input-item-box">
            <input
              className="code-input"
              placeholder="请输入您刚收到的验证码"
              type="tel"
              onChange={e => handleCodeInput(e)}
            />
          </div>
          <button className={getSubmitButtonClassName(code)} onClick={handleSubmit}>
            提交验证码
          </button>
        </div>
        <Loading isShow={showLoading} />
      </div>
    </div>
  )
}
