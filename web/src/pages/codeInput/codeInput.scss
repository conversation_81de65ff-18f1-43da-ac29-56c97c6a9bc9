input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.code-input-page-container {
  width: 7.5rem;
  .code-input-page {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fbca6b;
    .page-header-container {
      width: 2.96rem;
      height: 0.76rem;
      background: url(~@/assets/logo-title.svg) no-repeat center;
      background-size: contain;
      margin: 1.11rem auto 0 auto;
    }

    .input-wrap {
      width: 6.9rem;
      padding: 0.64rem 0.4rem 1.37rem 0.4rem;
      background-color: #ffffff;
      border-radius: 0.32rem;
      margin: 0.82rem auto 0 auto;

      .input-title {
        font-size: 0.48rem;
        line-height: 0.48rem;
        font-weight: 600;
        color: #12161c;
      }

      .input-sub-title {
        font-size: 0.36rem;
        line-height: 0.36rem;
        font-weight: 600;
        color: #12161c;
        margin-top: 0.25rem;
      }

      .input-phone-tips {
        font-size: 0.32rem;
        line-height: 0.36rem;
        font-weight: 600;
        color: #12161c;
        margin-top: 0.8rem;
      }

      .input-item-box {
        width: 100%;
        height: 1.04rem;
        border-bottom: 1px solid #d8d8d8;
        display: flex;
        align-items: center;

        .code-input {
          border: none;
          outline: none;
          font-size: 0.28rem;
          width: 100%;

          &:focus {
            border: none;
            outline: none;
            background: none;
          }
        }
      }

      .code-submit-button {
        width: 6.1rem;
        height: 0.96rem;
        border-radius: 0.45rem;
        background: #12161c;
        color: #ffffff;
        margin-top: 0.8rem;
        border: none;
        outline: none;
        font-size: 0.28rem;
        &.button-disabled {
          opacity: 0.4;
        }
      }
    }
  }
}
