import React from 'react'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Form, Button, Input, Select, DatePicker, Row, Col, Table, Tag, Tooltip } from 'antd'
import { PlainObject } from '@shark/core'

const { RangePicker } = DatePicker

export const ItemList: React.FC<{}> = (props: {}) => {
  const columns: any = [
    {
      title: '应用名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <a href="">{text}</a>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status'
    },
    {
      title: '创建时间',
      dataIndex: 'time',
      key: 'time',
      defaultSortOrder: 'descend',
      sorter: (a: PlainObject, b: PlainObject) =>
        Number(new Date(a.time)) - Number(new Date(b.time)),
      sortDirections: ['descend', 'ascend']
    },
    {
      title: '标签',
      key: 'tags',
      dataIndex: 'tags',
      render: (tags: any[]) => (
        <span>
          {tags.map((tag: any) => {
            let color = tag.length > 5 ? 'geekblue' : 'green'
            if (tag === 'loser') {
              color = 'volcano'
            }
            return (
              <Tag color={color} key={tag}>
                {tag.toUpperCase()}
              </Tag>
            )
          })}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (text: any, record: PlainObject) => (
        <span className="sharkr-table-actions">
          <Button className="action" type="link">
            操作1
          </Button>
          <Button className="action" type="link">
            操作2
          </Button>
          <Button className="action" type="link">
            操作3
          </Button>
          <Button className="action" type="link">
            操作4
          </Button>
        </span>
      )
    }
  ]
  const data: PlainObject[] = [
    {
      key: '1',
      name: '大促分析平台',
      status: '已上线',
      time: '2019-12-02 12:00:06',
      creator:
        '王力可、李晓阳、刘梦飞、孙佳、赵士大、罗毅杨、王力可、李晓阳、刘梦飞、孙佳、赵士大、罗毅杨',
      tags: ['nice', 'developer']
    },
    {
      key: '2',
      name: '供应链系统',
      status: '已上线',
      time: '2019-12-03 12:00:06',
      creator: '王力可、罗毅杨',
      tags: ['loser']
    },
    {
      key: '3',
      name: '商品管理平台',
      status: '已上线',
      time: '2019-12-01 12:00:06',
      creator: '孙佳',
      tags: ['cool', 'teacher']
    },
    {
      key: '4',
      name: '大促分析平台',
      status: '已上线',
      time: '2019-12-02 12:00:06',
      creator: '王力可、李晓阳、刘梦飞、孙佳、赵士大、罗毅杨',
      tags: ['nice', 'developer']
    },
    {
      key: '5',
      name: '供应链系统',
      status: '已上线',
      time: '2019-12-03 12:00:06',
      creator: '王力可、罗毅杨',
      tags: ['loser']
    },
    {
      key: '6',
      name: '商品管理平台',
      status: '已上线',
      time: '2019-12-01 12:00:06',
      creator: '孙佳',
      tags: ['cool', 'teacher']
    },
    {
      key: '7',
      name: '大促分析平台',
      status: '已上线',
      time: '2019-12-02 12:00:06',
      creator: '王力可、李晓阳、刘梦飞、孙佳、赵士大、罗毅杨',
      tags: ['nice', 'developer']
    },
    {
      key: '8',
      name: '供应链系统',
      status: '已上线',
      time: '2019-12-03 12:00:06',
      creator: '王力可、罗毅杨',
      tags: ['loser']
    },
    {
      key: '9',
      name: '商品管理平台',
      status: '已上线',
      time: '2019-12-01 12:00:06',
      creator: '孙佳',
      tags: ['cool', 'teacher']
    },
    {
      key: '10',
      name: '大促分析平台',
      status: '已上线',
      time: '2019-12-02 12:00:06',
      creator: '王力可、李晓阳、刘梦飞、孙佳、赵士大、罗毅杨',
      tags: ['nice', 'developer']
    },
    {
      key: '11',
      name: '供应链系统',
      status: '已上线',
      time: '2019-12-03 12:00:06',
      creator: '王力可、罗毅杨',
      tags: ['loser']
    },
    {
      key: '12',
      name: '商品管理平台',
      status: '已上线',
      time: '2019-12-01 12:00:06',
      creator: '孙佳',
      tags: ['cool', 'teacher']
    }
  ]

  return (
    <>
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">用户管理</span>
          <span className="sharkr-section-header-sub-title">（共19条）</span>
        </div>
        <div className="sharkr-section-content">
          <Form
            className="sharkr-form-inline margin-b-base"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}>
            <Row>
              <Col key="name" span={8} xl={8} xxl={6}>
                <Form.Item label="名称">
                  <Input className="sharkr-w-md" placeholder="placeholder" />
                </Form.Item>
              </Col>
              <Col key="city" span={8} xl={8} xxl={6}>
                <Form.Item label={<>城市</>}>
                  <Select className="sharkr-w-md" defaultValue="北京">
                    <Select.Option value="北京">北京</Select.Option>
                    <Select.Option value="上海">上海</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col key="birthday" span={8} xl={8} xxl={6}>
                <Form.Item label="提报时间">
                  <DatePicker className="sharkr-w-md" />
                </Form.Item>
              </Col>
              <Col key="type" span={8} xl={8} xxl={6}>
                <Form.Item label="采购方式">
                  <Input className="sharkr-w-md" placeholder="placeholder" />
                </Form.Item>
              </Col>
              <Col key="report" span={8} xl={8} xxl={6}>
                <Form.Item label="提报时间">
                  <RangePicker className="sharkr-w-lg" />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col key="submit">
                <Button className="margin-r-base" type="primary">
                  查询
                </Button>
                <Button className="margin-r-base"> 次按钮</Button>
                <Button className="margin-r-base">危险按钮</Button>
              </Col>
            </Row>
          </Form>
          <Table
            className="sharkr-table"
            columns={columns}
            dataSource={data}
            style={{ marginBottom: -16 }}
          />
        </div>
      </section>
    </>
  )
}
