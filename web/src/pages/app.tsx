/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-05 11:10:53
 * @FilePath: /yanxuan-rpa-common-server/web/src/pages/app.tsx
 * @Description: 根文件
 * @Author: <PERSON>wolf<PERSON> <EMAIL>
 */
import React from 'react'
import { message } from 'antd'
import { HashRouter as Router } from 'react-router-dom'
import {
  SharkRMenuProps,
  SharkRMenuItemClickEvent,
  SharkRMenuItem,
  SharkRIcon
} from '@sharkr/components'
import { Layout } from './layouts'
import './app.scss'
import { AppRoute } from './appRoute'

// root function
export const App: React.FC = () => {
  // const siderMenu: SharkRMenuProps = {
  //   data: [
  //     {
  //       name: '首页',
  //       icon: 'home',
  //       authCondition: 100,
  //       link: {
  //         href: '#/home'
  //       }
  //     },
  //     {
  //       name: '商品中心',
  //       icon: 'sharkr-shangpinxinxi',
  //       authCondition: 200,
  //       children: [
  //         {
  //           name: '商品列表',
  //           icon: 'item',
  //           authCondition: 2001,
  //           activeRouters: ['#/item/edit'],
  //           link: {
  //             href: '#/item/list'
  //           }
  //         },
  //         {
  //           name: '商品详情',
  //           icon: 'item',
  //           authCondition: 2002,
  //           link: {
  //             href: '#/item/detail'
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       name: '拦截链接跳转',
  //       icon: 'sharkr-xitongguanli',
  //       authCondition: 300,
  //       link: { href: '#/stop' },
  //       onItemClick: (e: SharkRMenuItemClickEvent) => {
  //         // 自定义回调事件
  //         message.info('拦截跳转')
  //         return false
  //       }
  //     },
  //     {
  //       name: '自定义render',
  //       link: { href: '#/set-render' },
  //       render: (item: SharkRMenuItem) => (
  //         <>
  //           <SharkRIcon type="sharkr-gongzuotai" />
  //           <span title="自定义render">
  //             {item.name}
  //             <span style={{ color: '#E7422B' }}>（10）</span>
  //           </span>
  //         </>
  //       )
  //     }
  //   ]
  // }

  return (
    <Router>
      <AppRoute />
      {/* <Layout siderMenu={siderMenu}>
      </Layout> */}
    </Router>
  )
}
