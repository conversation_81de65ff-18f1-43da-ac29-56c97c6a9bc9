/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-05 14:40:06
 * @FilePath: /yanxuan-rpa-common-server/web/src/pages/appRoute.tsx
 * @Description: route
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import React from 'react'
import { Route, Redirect, Switch } from 'react-router-dom'
import { CorePage404, CorePage500 } from '@sharkr/components'
import { UmcAuthGuard } from '@eagler/authorizion'
import { Home } from './home'
import { ItemList, ItemEdit, ItemDetail } from './item'
import { CodeInput } from './codeInput'

// 主函数
export const AppRoute: React.FC = () => (
  <Switch>
    <Route component={Home} key="/home" path="/home" />
    <Route
      key="/item/list"
      path="/item/list"
      render={() => <UmcAuthGuard component={ItemList} />}
    />
    <Route
      key="/item/detail"
      path="/item/detail"
      render={() => <UmcAuthGuard component={ItemDetail} />}
    />
    <Route
      key="/item/edit"
      path="/item/edit"
      render={() => <UmcAuthGuard component={ItemEdit} />}
    />
    <Route key="/codeInput" path="/codeInput" render={() => <CodeInput />} />
    <Redirect exact from="/" to="/home" />
    <Route component={CorePage500} key="/500" path="/500" />
    <Route component={CorePage404} />
  </Switch>
)
