<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <link rel="shortcut icon" href="favicon.ico">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="viewport"
    content="width=device-width, initial-scale=1, minmum-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no">
  <meta name="theme-color" content="#000000">
  <link rel="manifest" href="manifest.json">
  <title>严选中后台</title>
  <script type="text/javascript" compress="no-compress">
    !function () {
      function a() {
        var fontSize = null;
        if (parseInt(document.documentElement.clientWidth) > 750) {
          fontSize = 750 / 750 * 10 / 16 * 1000;
        } else {
          fontSize = document.documentElement.clientWidth / 750 * 10 / 16 * 1000;
        }

        //做下字体取整
        var normalized = (16 * fontSize / 100).toFixed(0);
        fontSize = normalized * 100 / 16;
        document.documentElement.style.fontSize = fontSize + "%";
      }
      var b = null;
      window.addEventListener("resize", function () {
        clearTimeout(b);
        b = setTimeout(a, 300);
      }, !1);
      a();
    }(window);
  </script>
</head>

<body>
  <noscript>
    You need to enable JavaScript to run this app.
  </noscript>
  <div id="root"></div>
</body>

</html>