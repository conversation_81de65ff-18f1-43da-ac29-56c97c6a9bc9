module.exports = {
  extends: ['@muses/yanxuan-b-lint', '@muses/yanxuan-b-lint/typescript'].map(require.resolve),
  plugins: ['@fe-sdk/comments'], // 必须
  rules: {
    // 这里填入你的项目需要的个性化配置，下列对于注释的约束必填：
    '@fe-sdk/comments/require-file-comment': 'warn',
    // "@fe-sdk/comments/require-function-comment": ["warn", {"excludeTags":["constructor","componentDidMount"]}]
    '@fe-sdk/comments/require-function-comment': 'warn',
    '@fe-sdk/comments/require-file-description': 'warn',
    '@fe-sdk/comments/require-file-author': 'warn',
    '@fe-sdk/comments/require-file-date': 'warn',
    '@fe-sdk/comments/require-function-description': 'warn',
    '@fe-sdk/comments/require-function-param': 'warn',
    '@fe-sdk/comments/require-function-param-description': 'warn',
    '@fe-sdk/comments/require-function-param-name': 'warn',
    '@fe-sdk/comments/require-function-param-type': 'warn',
    '@fe-sdk/comments/require-function-returns': 'warn',
    '@fe-sdk/comments/require-function-returns-description': 'warn',
    '@fe-sdk/comments/require-function-returns-type': 'warn',
    '@fe-sdk/comments/no-undefined-types': 'warn'
  },
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      parser: '@typescript-eslint/parser',
      rules: {
        '@fe-sdk/comments/require-interface-comment': 'warn',
        '@fe-sdk/comments/require-enum-comment': 'warn'
      }
    }
  ]
}
