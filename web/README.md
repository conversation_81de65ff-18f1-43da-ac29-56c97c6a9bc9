## 概述
B端前端工程，主要承载xxx等业务

工程名：`yanxuan-rpa-common-server`
## 功能简述

## 运行指南

### 1. 开发环境配置

#### 1.1 Host 配置

需要配置如下host：

`127.0.0.1 remote.yx.mail.netease.com`

`127.0.0.1 local.yx.mail.netease.com`

`************* dev.yx.localhost`

`************* dev.s.you.163.com`

建议使用Host管理工具来进行操作（如：SwitchHosts）

#### 1.2 VSCode

我们要求使用VSCode作为开发环境。请在VSCode中至少安装以下插件:

- ESLint

#### 1.3 Node 和 NPM

电脑需要正确安装node和npm，node版本为v14或以上，npm版本为v6或以上。
- 建议同学全局安装commitizen：npm install -g commitizen
- 建议同学安装VSCode插件：ESlint 和 koroFileHeader

#### 1.4 使用ppnpm

[使用ppnpm](http://yxpdc.mail.netease.com/friday/training-doc/tutorial/page/base.html#%E4%BD%BF%E7%94%A8ppnpm)

### 2. 开发过程

#### 2.1 安装依赖

目前工程依赖了严选 B 端组件库和基础库，而这些npm包发布在npm私服，需要使用ppnpm进行安装
[B端前端组件库](https://yx.mail.netease.com/shark/shark-doc-home/index.html)、
[AntDesign](https://ant.design/docs/spec/introduce-cn)

```bash
npm install 

```

#### 2.2 运行
```bash
npm run dev

```
执行上述命令后，浏览器会自动打开本地调试链接: `http://local.yx.mail.netease.com:9000/base`

#### 2.3 编译打包
```bash
npm run build  # 本地环境打包
npm run build:test  # 测试环境打包
npm run build:regression  # 回归环境打包
npm run build:online  # 线上环境打包

```

### 3. 部署

目前，工程已支持测试环境自动部署，各环境分支名及染色环境使用如下：

| 环境 | 分支 |
| --- | --- |
| 测试 | feature.* |
| 回归 | release-.* |
| 线上 | release-.* |
| bug修复 | hotfix-.* |

[云内染色环境使用说明](https://kttfkmbfmy.feishu.cn/docs/doccn5eJt2AoDsxjm02BwqmEm0g)

### 4. 提测

目前页面开发统一走[天枢](http://yx.mail.netease.com/dubhe#/versions/list)流程进行提测，因此需要在天枢中把对应的jira任务关联相应的git工程。

![提测](https://yanxuan.nosdn.127.net/static-union/16632959932c4ba4.png)

![关联分支](https://yanxuan.nosdn.127.net/static-union/166329610958cdc8.png)

输入服务名 `base`，选择对应的分支关联（需要git工程创建分支）。完成后点击右上角提测。

![提测信息](https://yanxuan.nosdn.127.net/static-union/1663296418b9e61f.png)

填写对应的提测信息，点击提交，这样完整的一个提测流程就完成了。

## 开发指南

本工程底层基于React框架生成，如需了解工程配套能力使用等更细粒度的开发指引，请查看
[B端前端开发统一文档](https://kttfkmbfmy.feishu.cn/wiki/wikcnTFLeIFK56fJ3LEwXY9U18f)

微前端配置及配置中心链接，请查看
[微前端配置](http://yx.mail.netease.com/doc/micro-fed#/home?tab=%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3&package=%E5%BE%AE%E5%89%8D%E7%AB%AF2.0%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3&section=%23%2Fdocument)

域名测试：http://test.yx.mail.netease.com/micro-fed/mf-apply/#/products-list?activeFirstTab=1

域名线上：http://yx.mail.netease.com/micro-fed/mf-apply/#/products-list?activeFirstTab=1


## 业务介绍

## CHANGELOG

## FAQ
