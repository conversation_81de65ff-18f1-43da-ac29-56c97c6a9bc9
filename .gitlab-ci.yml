variables:
  SERVICE_CODE: "yanxuan-rpa-common-server"
  ARTIFACT_PATH: $CI_PROJECT_NAME.zip # 云外打包文件名称
  ARTIFACT_YUN_PATH: ${CI_PROJECT_NAME}-yun.zip # 云上打包文件名称

stages:
  - install
  - package
  - deploy
  - upload-bee

before_script:
  - preCheck

install-deps:
  image: $CI_IMAGE_FRONT_NODE_14
  stage: install
  script:
    - pwd
    - sh scripts/install.sh
  tags:
    - ci-front
  cache:
    key: "$CI_PROJECT_NAME"
    # install 之后更新缓存
    policy: push
    paths:
      - node_modules/
      - web/node_modules/
      - server/node_modules/
  only:
    refs:
      - /^(dev|release|hotfix|feature).*$/
    changes:
      # 只在 package-lock.json 有变化时才执行该 stage
      - package-lock.json
      - web/package-lock.json
      - server/package-lock.json

# 目前feature做lint校验
package-lint:
  image: $CI_IMAGE_FRONT_NODE_14
  stage: package
  script:
    - npm run eslint
    - npm run eslint-stat -- --service=$SERVICE_CODE --uid=$GITLAB_USER_EMAIL --branch=$CI_COMMIT_REF_NAME
  cache:
    policy: pull
    key: "$CI_PROJECT_NAME"
    paths:
      - node_modules/
      - web/node_modules/
  tags:
    - ci-front
  only: # 只允许操作的分支
    - /^feature.*$/
  dependencies:
    - install-deps

.build_package: &build_definition
  image: $CI_IMAGE_FRONT_NODE_14
  stage: package
  script:
    - pwd # 绝对路径
    - npm run build:$PACKAGE_ENV # build，看代码是否报错
    - cp deploy/env/setenv_$PACKAGE_ENV.sh dist/setenv.sh # 拷贝deploy/env/setenv_test.sh 到 dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./* # 在dist下压缩云外资源
    - zip -rq ../$ARTIFACT_YUN_PATH ./* # 打包云上镜像
  artifacts: # 用于指定成功后应附加到job的文件和目录的列表 存附件
    paths:
      - $ARTIFACT_PATH
      - $ARTIFACT_YUN_PATH
    expire_in: 3d # 有效期
  cache:
    policy: pull
    key: "$CI_PROJECT_NAME"
    paths:
      - web/node_modules/
      - server/node_modules/
  tags:
    - ci-front

.deploy_artifacts_package: &deploy_artifacts_definition
  stage: deploy
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=$PACKAGE_ENV --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --module=$MODULE  --autoDeploy=true --clusterId=409
  tags:
    - ci-front

# 测试编译
package-fed-test:
  <<: *build_definition
  variables:
    PACKAGE_ENV: test
  only: # 只允许操作的分支
    - master
    - /^hotfix-.*$/
    - /^feature.+$/
    - /^release-.*$/
  dependencies:
    - install-deps

# 线上编译
package-fed-online:
  <<: *build_definition
  variables:
    PACKAGE_ENV: online
  only:
    - master
    - /^(release|hotfix).*$/
  dependencies:
    - install-deps

# 测试服上传代码制品
upload_artifacts-test:
  <<: *deploy_artifacts_definition
  variables:
    PACKAGE_ENV: test
    MODULE: default
  only: # 只允许操作的分支
    - dev
    - /^hotfix-.*$/
    - /^release-.*$/
    - /^feature.+$/
    - master
  dependencies:
    - package-fed-test

# 线上环境上传代码制品
upload_artifacts-online:
  stage: deploy
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=online --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --module=default
  tags:
    - ci-front
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - package-fed-online

upload-bee-online:
  stage: upload-bee
  script:
    - pwd
    - unzip $CI_PROJECT_NAME.zip -d artifacts
    - cd artifacts/src/swagger
    - beeUpload $SERVICE_CODE $CI_BUILD_REF_NAME $GITLAB_USER_NAME $GITLAB_USER_EMAIL
  tags:
    - ci-front
