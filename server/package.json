{"name": "yanxuan-rpa-common-server", "version": "1.0.0", "description": "node server & web dev template", "keywords": ["node", "koa", "angularx", "framework", "front-end", "web"], "license": "LGPL", "author": "the ntesmail authors", "scripts": {"server": "nodemon --config ./nodemon.json ./src/index.ts", "tslint": "tslint -c ./tslint.json './src/**/*.ts' -p ./tsconfig.json", "clean": "rm -rf build && rm -rf web", "build": "sh scripts/build/build.sh", "build:test": "sh scripts/build/build-test.sh", "build:betayun": "sh scripts/build/build-betayun.sh", "build:online": "sh scripts/build/build-online.sh", "build:regression": "sh scripts/build/build-regression.sh", "test": "jasmine-ts --config=test/jasmine.json"}, "dependencies": {"@eagle/common-service-node": "^4.0.22", "@eagle/workflow-node": "^4.0.0", "@eagler/authorizion-node": "^1.0.0", "@tiger/apolloy": "^4.4.1", "@tiger/boot": "^4.0.0", "@tiger/cache": "^4.0.0", "@tiger/core": "^4.1.1", "@tiger/ejs": "^4.0.0", "@tiger/error": "^4.0.0", "@tiger/filter": "^4.0.0", "@tiger/health": "^4.0.0", "@tiger/info": "^4.0.0", "@tiger/logger": "^4.0.0", "@tiger/microconfig": "0.0.3", "@tiger/openid": "^4.0.0", "@tiger/permission": "^4.0.0", "@tiger/proxy": "^4.0.0", "@tiger/request": "^4.0.0", "@tiger/security": "^4.0.0", "@tiger/session": "^4.0.0", "@tiger/swagger": "^4.0.0", "@tiger/tiger-extract-header": "0.0.4", "@tiger/validator": "^4.0.0", "@types/crypto-js": "^4.2.1", "boom": "^7.1.1", "crypto-js": "^4.2.0", "form-data": "^4.0.0", "ioredis": "^4.28.5", "koa": "2.13.0", "koa-body": "^4.0.4", "koa-compose": "^4.0.0", "koa-etag": "^3.0.0", "koa-router": "^7.3.0", "koa-send": "^4.1.2", "path-to-regexp": "^1.1.1"}, "devDependencies": {"@types/bluebird": "3.5.19", "@types/boom": "7.3.2", "@types/chai": "4.3.1", "@types/cookies": "0.7.8", "@types/ejs": "3.1.1", "@types/express": "4.17.3", "@types/express-serve-static-core": "4.17.28", "@types/ioredis": "^4.28.10", "@types/joi": "14.3.4", "@types/koa": "2.13.4", "@types/koa-compose": "3.2.5", "@types/koa-etag": "3.0.0", "@types/koa-router": "7.4.4", "@types/koa-send": "4.1.3", "@types/node": "17.0.41", "@vscode-snippets/tiger": "^1.0.0", "chai": "^4.2.0", "fs-extra": "^7.0.0", "get-port": "^3.2.0", "jasmine": "^3.3.1", "jasmine-ts": "^0.3.0", "nodemon": "^2.0.4", "supertest": "^3.3.0", "ts-node": "^6.1.1", "tslint": "^5.12.1", "typescript": "3.9.10", "yargs": "^7.0.2"}, "engines": {"node": ">=8.6.0"}}