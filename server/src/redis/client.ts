/*
 * @Description: Redis客户端
 * @Author: h<PERSON><PERSON><PERSON><PERSON> h<PERSON>@corp.netease.com
 */
import { Service } from '@tiger/boot'
import IORedis from 'ioredis'
import { appLogger } from '@tiger/logger'
import * as config from '../../src/conf'
import { RedisClient } from '../conf/types'

@Service
export class RedisClientService {
  private client: RedisClient

  constructor() {}

  get redis() {
    // 初始化ioredis连接
    if (this.client) {
      return this.client
    }
    appLogger.info(`开始连接Redis！redisConfig: ${JSON.stringify(config.redisConfig)}`)

    const options = config.redisConfig

    if (options.cluster === true) {
      const nodes = options.nodes || []
      if (!nodes || !nodes.length) {
        throw new Error('Redis配置异常！cluster节点列表为空！')
      }
      this.client = new IORedis.Cluster(nodes, options)
    } else if (options.sentinels) {
      if (!options.sentinels || !options.sentinels.length) {
        throw new Error('Redis配置异常！sentinels节点列表为空！')
      }
      this.client = new IORedis(options)
    } else {
      this.client = new IORedis(options)
    }

    return this.client

    // // 监听连接事件
    // this.redis.on('connect', () => {
    //   appLogger.info('Redis连接成功')
    // })

    // this.redis.on('error', (error) => {
    //   appLogger.error(`Redis连接错误: ${error}`)
    // })

    // this.redis.on('close', () => {
    //   appLogger.warn('Redis连接关闭')
    // })
  }

  /**
   * 设置缓存值
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间（秒），默认7200秒（2小时）
   */
  async set(key: string, value: any, ttl: number = 7200): Promise<boolean> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value))
      return true
    } catch (error) {
      appLogger.error(`Redis设置缓存失败: key=${key}, error=${error}`)
      return false
    }
  }

  /**
   * 获取缓存值
   * @param key 缓存键
   * @returns 缓存值，不存在返回null
   */
  async get(key: string): Promise<any> {
    try {
      const value = await this.redis.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      appLogger.error(`Redis获取缓存失败: key=${key}, error=${error}`)
      return null
    }
  }

  /**
   * 删除缓存
   * @param key 缓存键
   */
  async delete(key: string): Promise<boolean> {
    try {
      await this.redis.del(key)
      return true
    } catch (error) {
      appLogger.error(`Redis删除缓存失败: key=${key}, error=${error}`)
      return false
    }
  }

  /**
   * 检查缓存是否存在
   * @param key 缓存键
   */
  async has(key: string): Promise<boolean> {
    try {
      const exists = await this.redis.exists(key)
      return exists === 1
    } catch (error) {
      appLogger.error(`Redis检查缓存存在性失败: key=${key}, error=${error}`)
      return false
    }
  }

  /**
   * 获取所有匹配模式的键
   * @param pattern 匹配模式，如 "channelCode:*"
   */
  async keys(pattern: string): Promise<string[]> {
    try {
      // 使用ioredis的keys命令
      const keys = await this.redis.keys(pattern)
      return keys || []
    } catch (error) {
      appLogger.error(`Redis获取键列表失败: pattern=${pattern}, error=${error}`)
      return []
    }
  }

  /**
   * 设置带过期时间的缓存
   * @param key 缓存键
   * @param value 缓存值
   * @param expireAt 过期时间戳（毫秒）
   */
  async setWithExpireAt(
    key: string,
    value: any,
    expireAt: number
  ): Promise<boolean> {
    try {
      await this.redis.set(key, JSON.stringify(value))
      await this.redis.expireat(key, Math.floor(expireAt / 1000))
      return true
    } catch (error) {
      appLogger.error(`Redis设置带过期时间缓存失败: key=${key}, error=${error}`)
      return false
    }
  }

  /**
   * 批量删除匹配模式的键
   * @param pattern 匹配模式
   */
  async deleteByPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.keys(pattern)
      if (keys.length === 0) {
        return 0
      }
      const deletedCount = await this.redis.del(...keys)
      return deletedCount
    } catch (error) {
      appLogger.error(
        `Redis批量删除缓存失败: pattern=${pattern}, error=${error}`
      )
      return 0
    }
  }

  /**
   * 关闭Redis连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.redis) {
        this.redis.disconnect()
        appLogger.info('Redis连接已关闭')
      }
    } catch (error) {
      appLogger.error(`关闭Redis连接失败: ${error}`)
    }
  }
}
