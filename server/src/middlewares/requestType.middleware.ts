/*
 * @Date: 2024-01-25 15:45:57
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-19 15:12:19
 * @FilePath: /yanxuan-rpa-common-server/server/src/middlewares/requestType.middleware.ts
 * @Description: 校验请求体类型的中间件
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import { Context } from "koa";
import { appLogger } from "@tiger/logger";
import { AjaxResult } from "@tiger/core";

async function checkJsonRequest(ctx: Context, next: any) {
  const { headers } = ctx.request;
  if (headers["content-type"] !== "application/json") {
    appLogger.error(`请求体类型错误：${headers["content-type"]}`);
    ctx.body = AjaxResult.badRequest("请求体类型错误");
    return;
  }
  await next();
}

async function checkFormRequest(ctx: Context, next: any) {
  const { headers } = ctx.request;
  if (headers["content-type"] !== "application/x-www-form-urlencoded") {
    appLogger.error(`请求体类型错误：${headers["content-type"]}`);
    ctx.body = AjaxResult.badRequest("请求体类型错误");
    return;
  }
  await next();
}

export {
  checkJsonRequest,
  checkFormRequest
}