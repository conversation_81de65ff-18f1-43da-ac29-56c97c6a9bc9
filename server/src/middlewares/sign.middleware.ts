/*
 * @Date: 2024-01-04 10:45:31
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-19 15:38:20
 * @FilePath: /yanxuan-rpa-common-server/server/src/middlewares/sign.middleware.ts
 * @Description: 用于sign鉴权的中间件
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

import { Context } from "koa";
import md5 from "crypto-js/md5";
import { AjaxResult } from "@tiger/core";
import { ApolloyConfig } from "../apolloy";
import { appLogger } from "@tiger/logger";
import compose from "koa-compose";
import { checkFormRequest, checkJsonRequest } from "./requestType.middleware";

// 对python语言的签名鉴权
async function validSign(ctx: Context, next: any) {
  const { body, query } = ctx.request;
  const appSecret = ApolloyConfig["rpa.base.appSecret"];
  if ((!body?.sign || !body?.requestTime) && (!query?.sign || !query?.requestTime)) {
    appLogger.error(`鉴权失败，缺少鉴权参数：${JSON.stringify({ ...body, ...query })}`);
    ctx.body = AjaxResult.unauthorized("鉴权失败");
  } else {
    let target = body
    if (!body.sign) {
      target = query;
    }
    let { sign, requestTime, requestContentStr } = target;
    const curTiemStamp = new Date().getTime();
    if (curTiemStamp - requestTime > 60 * 1000) {
      appLogger.error(`鉴权失败，请求已过期：${JSON.stringify({ ...body })}`);
      ctx.body = AjaxResult.unauthorized("鉴权失败");
      return;
    }
    const signStr = `${appSecret}requestTime=${requestTime}&requestContentStr=${requestContentStr}${appSecret}`;
    const signMd5 = md5(signStr).toString();
    if (signMd5 === sign) {
      if (body.sign) {
        try {
          delete ctx.request.body.sign;
          delete ctx.request.body.requestTime;
          ctx.request.body = JSON.parse(requestContentStr);
          appLogger.info(`${ctx.path}请求参数：${requestContentStr}`);
        } catch (error) {
          appLogger.error(`POST请求删除多余参数异常：${JSON.stringify({ ...body })}`);
          ctx.body = AjaxResult.internal("鉴权异常");
          return;
        }
      } else {
        try {
          ctx.request.query = JSON.parse(requestContentStr);
        } catch (error) {
          appLogger.error(`GET请求删除多余参数异常：${JSON.stringify({ ...query })}`);
          ctx.body = AjaxResult.internal("鉴权异常");
          return;
        }
      }
      await next();
    } else {
      appLogger.error(`鉴权失败，sign不匹配：${JSON.stringify({ ...body })}`)
      ctx.body = AjaxResult.unauthorized("鉴权失败");
      return;
    }
  }
}

// 对js语言的签名鉴权
// async function validSignForJS(ctx: Context, next: any) {
//   const { body, query } = ctx.request;
//   const appSecret = ApolloyConfig["rpa.base.appSecret"];
//   if (!body?.sign && !query?.sign) {
//     appLogger.error(`鉴权失败，缺少sign：${JSON.stringify({ ...body, ...query })}`);
//     ctx.body = AjaxResult.unauthorized("鉴权失败");
//   } else {
//     let target = body?.sign ? body : query;
//     const { sign, ...rest } = target;
//     const keys = Object.keys(rest).sort();
//     const str = keys.reduce((acc, cur) => {
//       if (typeof cur === 'object') {
//         return `${acc}${cur}=${JSON.stringify(rest[cur])}&`;
//       }
//       return `${acc}${cur}=${rest[cur]}&`;
//     }, "");
//     const signStr = `${appSecret}${str.slice(0, -1)}${appSecret}`;
//     const signMd5 = md5(signStr).toString();
//     if (signMd5 === sign) {
//       await next();
//     } else {
//       appLogger.error(`鉴权失败，sign不匹配：${JSON.stringify({ ...body, ...query })}`)
//       ctx.body = AjaxResult.unauthorized("鉴权失败");
//     }
//   }
// }

// const validSignForPythonWithHeaderCheck = compose([checkJsonRequest, validSign])
// const validSignForJSWithHeaderCheck = compose([checkJsonRequest, validSignForJS])

export {
  validSign
}