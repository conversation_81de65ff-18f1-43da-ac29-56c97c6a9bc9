/*
 * @Date: 2024-01-30 16:48:11
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 15:32:49
 * @FilePath: /yanxuan-rpa-common-server/server/src/middlewares/openProxy.middleware.ts
 * @Description: 为开放的转发代理配置的中间件，用于鉴权相关
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

import filter from "@tiger/filter"
import { validSign } from "./sign.middleware";

const OpenProxyValidate = (() => {
  // 通过filter方法进行封装，扩展根据路由灵活匹配的功能
  return filter(validSign)
})()

export default OpenProxyValidate;

