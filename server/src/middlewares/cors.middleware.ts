/*
 * @Date: 2025-03-05 15:58:02
 * @LastEditors: hz<PERSON><EMAIL>
 * @LastEditTime: 2024-01-19 17:12:15
 * @FilePath: server/src/middlewares/cors.middleware.ts
 * @Description: 接口支持跨域访问
 * @Author: <EMAIL>
 */


import { Context } from "koa";
import filter from "@tiger/filter"

const Cors = async (ctx: Context, next: any)=> {
  ctx.set('Access-Control-Allow-Origin', '*');
  ctx.set('Access-Control-Allow-Headers', 'Content-Type, Content-Length, Authorization, Accept, X-Requested-With , yourHeaderFeild');
  ctx.set('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
  if (ctx.method == 'OPTIONS') {
    ctx.body = 200; 
  } else {
    await next();
  }
}



const CorsMiddleware = (() => {
  // 通过filter方法进行封装，扩展根据路由灵活匹配的功能
  return filter(Cors)
})()

export default CorsMiddleware;

