/*
 * @Date: 2024-01-08 15:58:02
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-19 17:12:15
 * @FilePath: /yanxuan-rpa-common-server/server/src/middlewares/symAuth.middleware.ts
 * @Description: 对称加密算法的鉴权中间件
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

/**
 * 校验规则：
 * 1. 使用aes256方式对称加密解密
 * 2. 明文内容为：机器人名称+时间戳 例如：robot-1 1638952200000 中间以空格分隔，机器人名称不允许有空格
 * 3. 密钥存储在apollo配置中心以及各机器人服务器的本地文件中
 * 4. 密钥名称为：rpa.nosUpload.appSecret
 * 5. 未知的机器人名称以及超出5min的时间戳均视为鉴权失败
 * 6. 加密后的token存储在请求头中的 Rpa-Sym-Token 字段中
 */

import { Context } from "koa";
import AES from 'crypto-js/aes';
import { ApolloyConfig } from "../apolloy";
import { AjaxResult } from "@tiger/core";

export default async function symAuth(ctx: Context, next: any) {
  const { headers } = ctx.request;
  const { 'Rpa-Sym-Token': token } = headers;
  const decrypted = AES.decrypt(token as string, ApolloyConfig['rpa.aes.appSecret']);
  const decryptedStr = decrypted.toString();
  const [name, timestamp] = decryptedStr.split(' ');
  const nameList = ApolloyConfig['rpa.robot.nameList'];
  if (!name || !timestamp) {
    ctx.body = AjaxResult.unauthorized('鉴权失败');
    return;
  }
  if (!nameList.includes(name)) {
    ctx.body = AjaxResult.unauthorized('鉴权失败');
    return;
  }
  if (Date.now() - Number(timestamp) > 5 * 60 * 1000) {
    ctx.body = AjaxResult.unauthorized('请求已过期，鉴权失败');
    return;
  }
  await next();
}