/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 14:47:42
 * @FilePath: /yanxuan-rpa-common-server/server/src/bin/_bootstrap.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
// tslint:disable-next-line:file-name-casing
import Apollo from '@tiger/apolloy';

Apollo.appId = 'yanxuan-rpa-common-server';
Apollo.baseCachePath = './dist';
Apollo.initConfigurations();
Apollo.listenNotifications('default', ['application', 'olympus-pub.robotList'],
  { 'dev': 'http://test.yx.mail.netease.com/apolloy-config' });

import '../conf';

