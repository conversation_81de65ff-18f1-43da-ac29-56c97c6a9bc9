/*
 * @Date: 2024-01-05 17:07:55
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 14:43:25
 * @FilePath: /yanxuan-rpa-common-server/server/src/apolloy/index.ts
 * @Description: apolloy配置
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
import { ApolloConfigChangeListener, EnableApolloConfig, Value } from '@tiger/apolloy';
import { appLogger } from '@tiger/logger';

@EnableApolloConfig('application')
class Configuration {

  @Value('rpa.base.appSecret', '')
  'rpa.base.appSecret': string

  @Value('rpa.aes.appSecret', '')
  'rpa.aes.appSecret': string

  @Value('rpa.aes.iv', '')
  'rpa.aes.iv': string

  @Value('rpa.robot.nameList', [])
  'rpa.robot.nameList': string[]

  @Value('JD.config', '')
  'JD.config': string

  @Value('DY.config', '')
  'DY.config': string

  @Value('TX.config', '')
  'TX.config': string

  @Value('PDD_CONFIG', '')
  'PDD_CONFIG': string

  @Value('CHANNEL_TAG', '')
  'CHANNEL_TAG': string

  @Value('api.service.list')
  'api.service.list': string

  @ApolloConfigChangeListener('application')
  someOnChange(fields: string[]) {
    appLogger.info('apolloy application config changed: ' + fields.toString());
  }
}

@EnableApolloConfig('olympus-pub.robotList')
class RobotListConfiguration {
  @Value('channelRobotMap', '')
  'channelRobotMap': string

  @Value('channelAppIdMap', '')
  'channelAppIdMap': string

  @Value('externalFlowAppConfig', '')
  'externalFlowAppConfig': string

  @ApolloConfigChangeListener('olympus-pub.robotList')
  someOnChange(fields: string[]) {
    appLogger.info('apolloy olympus-pub.robotList config changed: ' + fields.toString())
  }
}

const ApolloyConfig = new Configuration();
const RobotListConfig = new RobotListConfiguration();

const getChannelTag = () => {
  const channelTagStr = ApolloyConfig["CHANNEL_TAG"];
  return JSON.parse(channelTagStr || '{}');
}

const getApiServiceList = () => {
  return JSON.parse(ApolloyConfig['api.service.list'] || '[]')
}


export {
  ApolloyConfig,
  getChannelTag,
  RobotListConfig,
  Configuration,
  RobotListConfiguration,
  getApiServiceList,
}