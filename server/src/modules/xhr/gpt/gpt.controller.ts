import {
    PostMapping,
    RequestMapping,
    RestController
} from '@tiger/boot';
import {AjaxResult, RequestContext} from '@tiger/core';
import { GptChatBodyParams, GptChatQueryParams, GptChatResult } from './vo/gpt.po';
import { GPTService } from './gpt.service';
import { appLogger } from '@tiger/logger';
import { validSign } from '../../../middlewares/sign.middleware';
import config from '../../../conf';


/**
 * openai对接
 */
@RestController
@RequestMapping(
    '/gpt'
    ,[validSign]
)
export class GPTController {
    constructor(private gptService: GPTService) { }
    
    /**
     * 调用原生接口（一次性返回）
     */
    @PostMapping('/openai/chat')
    public async gptChat(ctx: RequestContext<GptChatBodyParams, AjaxResult<GptChatResult >,GptChatQueryParams>) {
      if (!ctx.request.body||!ctx.request.body.messages) {
        return ctx.body = AjaxResult.badRequest('messages不能为空');
      }
      if (!ctx.request.query.product) {
        return ctx.body = AjaxResult.badRequest('产品号不能为空');
      }
      // 设置接口超时时间10分钟
      ctx.request.socket.setTimeout(10*60*1000);

      const {product,open_ai_model='gpt-3.5-turbo-0125'} = ctx.request.query;
      let message = ''
      try {
        const params = {
            model: open_ai_model,
            ...ctx.request.body
        }
        const headers = {
            "Content-Type": "application/json",
            "product": "rpa",
            "timeout":600000,
            "service": open_ai_model,
            "NTES-CNGINX-TIMEOUT-CONN": 900 as any,
            "NTES-CNGINX-TIMEOUT-READ": 900 as any,
            "NTES-CNGINX-TIMEOUT-SEND": 900 as any,
        }
        const rst = await this.gptService.chat(headers, params);
        appLogger.info(`调用openai接口返回结果：产品号：${product};${JSON.stringify(rst)}`);
        message = `product=${product},message=调用GPT成功`
        return ctx.body = AjaxResult.success(rst);
      } catch (error) {
        message = `product=${product},message=调用GPT失败，原因: ${JSON.stringify(error || '')}}`
        appLogger.error(message);
        return ctx.body = AjaxResult.internal(`调用GPT失败，原因: ${JSON.stringify(error || '')}}`);
      }finally {
        this.gptService.logToFileSync(message, config.loggerPath+'/gpt.log');
      }
    }
}
