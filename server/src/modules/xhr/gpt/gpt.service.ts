import { Service } from '@tiger/boot';
import { appLogger } from '@tiger/logger';
import { getErrorMessage } from '../../../util/util';
import { GptChatResult } from './vo/gpt.po';
import { getServiceUrl } from '@tiger/core';
import  axios  from '@tiger/request';
import * as fs from 'fs';

@Service
export class GPTService{
   gptServices: any = {
    dev: 'https://rpa.test.you.163.com/base/xhr/nlp',
    test: 'http://127.0.0.1:8550/p/test.yanxuan-nlp-server.service.mailsaas',
    regression: 'http://127.0.0.1:8550/p/regression.yanxuan-nlp-server.service.mailsaas',
    online: 'http://127.0.0.1:8550/p/online.yanxuan-nlp-server.service.mailsaas',
  }

  async chat(headers:any,params:any): Promise<GptChatResult>{
        try {
          const url = getServiceUrl(this.gptServices) + '/openai/chat';
          const res = await axios.post(url, params,{headers});
          return res.data.data;
        } catch (error) {
          appLogger.error(`调用openai接口报错:${getErrorMessage(error)}`);
          throw new Error(`调用openai接口报错:${getErrorMessage(error)}`);
        }
  }

  logToFileSync(message:string, logFilePath:string) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    try {
        if (!fs.existsSync(logFilePath)) {
          fs.writeFileSync(logFilePath, '', 'utf8');
        }
        fs.appendFileSync(logFilePath, logMessage, 'utf8');
    } catch (err) {
      appLogger.error(`Error writing to log file:${JSON.stringify(err)}`);
    }
  }

}
