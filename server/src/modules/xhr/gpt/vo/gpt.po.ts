
export class GptChatQueryParams{
   /**
   * 产品号，用于统计结算调用费用
   */
  product:string;
  // 模型，默认值：gpt-3.5-turbo-0125
  open_ai_model?:string;
  
}

export class GptChatBodyParams{
  // 取值范围[0,1]，值越大生成结果的【多样性/创造性】越大，相应的模型对结果的【不确定性】越大，当temperature设置为0，多次提交结果返回一致。默认值：0.5
  temperature:number;
  // 返回的最大文本token数量，默认值：100
  maxTokens:number;
  // 参考openai 接口参数
  messages: any[];
  [k: string]: any;
}

export class GptChatResult {
  /**
   * 结果状态
   */
  status: string;
  /**
   * 描述
   */
  desc: string
  // data
  data: GptChatData;
}

export class GptChatData {
  /** 
   * ID 
   */
  id: number;
  /** 
   * 功能对象
   */
  object: string;
  /** 
   * 模型 
   */
  model: string;
  /** 
   * 使用数据
   */
  usage: any;
  /** 
   * 问题结果
   */
  choices: any[];
}

export class UserEmail {
  /** 
   * 用户邮箱 
   */
  email: string;
  /** 
   * 用户名
   */
  name: string;
}