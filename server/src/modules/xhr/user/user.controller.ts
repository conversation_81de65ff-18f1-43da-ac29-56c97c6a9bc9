import {
    GetMapping,
    PostMapping,
    RequestMapping,
    RestController
} from '@tiger/boot';
import {AjaxResult, AppConfig, QueryContext} from '@tiger/core';
import {valid} from '@tiger/validator';
import {NameVO} from './vo/name.vo';
import {UserResultVO} from './vo/user-result.vo';

/**
 * 用户管理
 */
@RestController
@RequestMapping(
    '/user',
    []
)
export class UserController {
    /**
     * 查询当前用户信息
     */
    @GetMapping('/getUserInfo.json')
    getUserInfo(ctx: QueryContext<null, AjaxResult<UserResultVO>>) {
        ctx.body = AjaxResult.success(ctx.openIDInfo);
    }
    /**
     * 查询当前用户信息
     */
    @GetMapping('/getMockData.json')
    getMockData(ctx: QueryContext<null, AjaxResult<any>>) {
        ctx.body = AjaxResult.success({
            newRefundAddress:[{
                province:"浙江省",
                city:"杭州市",
                district:"滨江区",
                town:"长河街道",
                detail:"网商路599号",
                name:"猪猪",
                mobile:"15268628187",
                zipCode:""
            },{
                province:"浙江省",
                city:"杭州市",
                district:"滨江区",
                town:"长河街道",
                detail:"网商路399号",
                name:"猪猪2",
                mobile:"15268628187",
                zipCode:""
            }],
            itemRefundAddress:[
                {
                    item: [
                        {
                            itemId: "3662100198643122798",
                            outItemId: "",
                            skuId: "",
                            outSkuId: ""
                        }
                    ],
                    refundAddress: {
                        province:"浙江省",
                        city:"杭州市",
                        district:"滨江区",
                        town:"长河街道",
                        detail:"网商路599号",
                        name:"猪猪",
                        mobile:"15268628187",
                        zipCode:""
                    }
                },
                {
                    item: [
                        {
                            itemId: "3662099934452297503",
                            outItemId: "",
                            skuId: "",
                            outSkuId: ""
                        }
                    ],
                    refundAddress: {
                        province:"浙江省",
                        city:"杭州市",
                        district:"滨江区",
                        town:"长河街道",
                        detail:"网商路399号",
                        name:"猪猪2",
                        mobile:"15268628187",
                        zipCode:""
                    }
                }
            ]
        });
    }
    @PostMapping('/valid.do', [valid(NameVO)])
    getHello(ctx: QueryContext<NameVO, AjaxResult<NameVO>>) {
        ctx.body = AjaxResult.success(ctx.query);
    }
}
