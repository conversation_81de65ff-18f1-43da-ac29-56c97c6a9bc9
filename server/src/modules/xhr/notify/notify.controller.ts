/*
 * @Date: 2024-01-24 11:39:36
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 18:21:53
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/notify/notify.controller.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import {
  PostMapping,
  RequestMapping,
  RestController
} from '@tiger/boot';
import { AjaxResult, RequestContext } from '@tiger/core';
import { MultiMsgResult, NotifyService, SingleMsgResult } from '@eagle/common-service-node';
import { EmailMsg, NotifyParamsPO } from './vo/notify.po';
import { validSign } from '../../../middlewares/sign.middleware';

/**
 *  消息通知 controller
 */
@RestController
@RequestMapping(
  '',
  [validSign]
)
export class NotifyController {
  constructor(private notifyService: NotifyService) {
  }
  /**
    *
    * @description 发送消息，该接口通过调用严选msg服务，推送相关消息，
    *              该服务仅限公司内部人员的消息推送，如想推送外部人员的短信消息，请调用uas服务
    */
  @PostMapping('/notify')
  public async notify(ctx: RequestContext<NotifyParamsPO, AjaxResult<string | SingleMsgResult | MultiMsgResult>>) {
    if (!ctx.request.body) {
      return ctx.body = AjaxResult.badRequest('参数错误');
    }
    const { type, msgInfo } = ctx.request.body;
    try {
      const rst = await this.notifyService.notify(type, msgInfo);
      return ctx.body = AjaxResult.success(rst);
    } catch (error) {
      return ctx.body = AjaxResult.internal(`发送消息失败: ${JSON.stringify(error || '')}}`);
    }
  }

    /**
    *
    * @description 发送邮件消息 支持传附件
    */
  @PostMapping('/emailMsg')
  public async emailWithAttachment(ctx: RequestContext<EmailMsg, AjaxResult<string | SingleMsgResult >>) {
    if (!ctx.request.body) {
      return ctx.body = AjaxResult.badRequest('参数错误');
    }
    const params = ctx.request.body;
    try {
      const rst = await this.notifyService.singleMsg('email', params);
      return ctx.body = AjaxResult.success(rst);
    } catch (error) {
      return ctx.body = AjaxResult.internal(`发送消息失败: ${JSON.stringify(error || '')}}`);
    }
  }

}
