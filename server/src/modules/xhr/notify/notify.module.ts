/*
 * @Date: 2024-01-24 11:39:36
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-24 14:27:37
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/notify/notify.module.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import { TgModule } from '@tiger/boot';
import { NotifyController } from './notify.controller';

@TgModule({
  prefix: '/message',
  imports: [],
  controllers: [NotifyController]
})
export class NotifyModule { }
