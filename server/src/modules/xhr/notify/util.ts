/*
 * @Date: 2024-01-29 15:23:58
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-29 15:27:42
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/notify/util.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
type MsgMethodType = 'MultiMsg' | 'EmailMsg' | 'PopoMsg' | 'MobileMsg' | 'YixinMsg' | 'VoiceMsg' | 'FeishuMsg'

export const generateMsgId = (type: MsgMethodType): string => {
  const randomCode = generateRandomCode(16)
  return `yanxuan-rpa-common-server-${type}-${randomCode}`
}

const generateRandomCode = (length: number) => {
  var result = '';
  var characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}