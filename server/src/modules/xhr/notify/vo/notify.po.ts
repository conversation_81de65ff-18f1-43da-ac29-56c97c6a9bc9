export class NotifyParamsPO {
  /**
   * 消息类型
   */
  type: 'multi' | 'popo' | 'email' | 'feishu' | 'mobile' | 'voice';
  /**
   * 信息内容
   */
   msgInfo: MultiMsg;
}


export class MultiMsg {
  /**
   * 邮件消息
   */
  emailMsg: EmailMsg;
  /**
   * 飞书消息
   */
  feishuMsg: FeishuMsg;
  /**
   * 短信消息
   */
  mobileMsg: MobileMsg;
  /**
   * popo消息
   */
  popoMsg: PopoMsg;
  /**
   * 飞书消息
   */
  voiceMsg: VoiceMsg;
  /**
   * 易信消息
   */
  yixinMsg: YixinMsg;
}
export class EmailMsg {
  // 附件 附件列表（需要先调用文件上传接口:/notify/{product}/file/upload.json上传文件
  attachments: Attachment[];
  // 密送人列表（会过滤掉越权用户）
  bccList: string[];
  // 抄送人列表（会过滤掉越权用户）
  ccList: string[];
  // 消息内容【必传】 富文本消息时仅设置标题即可 
  content: string;
  // 消息级别(1:INFO, 2:WARN）
  level: number;
  // 消息id(保证唯一,用于消息链路追踪)【必传】
  messageId: string;
  // 设置发件人的名称（默认为网易严选消息提醒）
  name: string;
  // 消息接受者列表(该字段不做校验)【该字段一般不使用,使用时需要单独申请】邮件消息:不在严选组织架构内的内部邮箱用户
  receivers: string[];
  // 邮件主题【必传】
  subject: string;
  // 接收人邮箱列表【接收人优先使用该字段】
  uids: string[];
}
export class Attachment {
  // 文件名称
  fileName: string;
  // 文件key
  url: string;
}
export class FeishuMsg {
  // 卡片消息交互场景时的回调接口地址
  cardCallbackUrl: string;
  // 消息内容【必传】飞书消息:长度30k以内,富文本消息时仅设置标题即可
  content: string;
  // 选择发送飞书消息的应用(默认为严选内网,其他详见:https://kttfkmbfmy.feishu.cn/docs/doccn6fYIs98sic6LNIMgsW5Y0f)
  from: string;
  // 消息级别(1:INFO, 2:WARN）
  level: number;
  // 消息id(保证唯一,用于消息链路追踪)【必传】
  messageId: string;
  // 飞书消息类型(调用方不感知)
  msgType: string;
  // 消息接受者列表(该字段不做校验)【该字段一般不使用,使用时需要单独申请】飞书消息:群内消息chat_id包含在这个list中
  receivers: string[];
  richTextContent: RichTextContent;
  // 是否群内消息(默认为false)
  send2Chat: boolean;
  // 是否是发送富文本消息(默认为false)
  send2RichText: boolean;
  // 是否是消息卡片(默认为false)
  sendCardMsg: boolean;
  // 接收人邮箱列表【接收人优先使用该字段】
  uids: string[];
}
export class RichTextContent {
  content: Content[][];
  title: string;
}
export class Content {
  emoji_type: string;
  file_key: string;
  href: string;
  image_key: string;
  tag: string;
  text: string;
  user_id: string;
  user_name: string;
}
export class MobileMsg {
  content: string;
  level: number;
  messageId: string;
  receivers: string[];
  uids: string[];
}
export class PopoMsg {
  content: string;
  from: string;
  level: number;
  messageId: string;
  receivers: string[];
  teamMessage: boolean;
  uids: string[];
}
export class VoiceMsg {
  content: string;
  level: number;
  messageId: string;
  receivers: string[];
  uids: string[];
}
export class YixinMsg {
  content: string;
  level: number;
  messageId: string;
  receivers: string[];
  uids: string[];
}