import {
    GetMapping,
    RequestMapping,
    RestController
} from '@tiger/boot';
import {AjaxResult, QueryContext} from '@tiger/core';
import { PddShopInfoPO, TiggerTaskParams } from './vo/pdd.po';
import { PddService } from './pdd.service';
import { YingdaoService } from '../../api/yingdao/yingdao.service';
import { appLogger } from '@tiger/logger';
import config from '../../../conf';
import { ParamType } from '../../api/yingdao/yingdao.type';

/**
 * 用户管理
 */
@RestController
@RequestMapping(
    '/pdd'
)
export class PddController {

    constructor(private pddService: PddService,private yingdaoService:YingdaoService) { }
    
    /**
     * 查询当前用户信息
     */
    @GetMapping('/getUserInfo.json')
    getUserInfo(ctx: QueryContext<null, AjaxResult<PddShopInfoPO>>) {
        const shopInfo = this.pddService.getShopsFromApolloY();
        ctx.body = AjaxResult.success(shopInfo);
    }

    /**
     * 手动触发任务
     */
        @GetMapping('/tiggerTask.json')
        tiggerTask(ctx: QueryContext<TiggerTaskParams, AjaxResult<any>>) {
            const {account} = ctx.query;
            const params = {
                "scheduleUuid":"8efe153f-5d1c-4840-9e9b-9d0824a6db5b",
                "scheduleRelaParams":[{
                    "robotUuid":"dc3059c6-79c5-4bb3-9865-71b135165221",
                    "params":[
                        {
                         "name":"account",
                         "value":account,
                         "type": ParamType.STRING
                        },
                        {
                            "name":"env",
                            "value":config.env,
                            "type":ParamType.STRING
                        }
                    ]
                }]
            }
            appLogger.info(`手动触发任务：${JSON.stringify(params)}`);
            const task = this.yingdaoService.tiggerTask(params);
            ctx.body = AjaxResult.success(task,`任务触发成功，店铺账号为${account}`);
        }
}
