import { Service } from '@tiger/boot';
import { PddShopInfoPO } from './vo/pdd.po';
import { ApolloyConfig } from '../../../apolloy';
import { appLogger } from '@tiger/logger';
import { getErrorMessage } from '../../../util/util';

@Service
export class PddService{
    getShopsFromApolloY(): PddShopInfoPO{
        try {
          const shopConfigStr = ApolloyConfig["PDD_CONFIG"];
          return JSON.parse(shopConfigStr);
        } catch (error) {
          appLogger.error(`获取Pdd店铺配置错误:${getErrorMessage(error)}`);
          throw new Error(`获取Pdd店铺配置错误:${getErrorMessage(error)}`);
        }
    }
}
