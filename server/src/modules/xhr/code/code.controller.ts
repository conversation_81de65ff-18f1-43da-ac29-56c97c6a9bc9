/*
 * @Date: 2024-01-04 15:28:33
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-29 16:17:38
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/code.controller.ts
 * @Description: 
 * @Author: <PERSON>wolf<PERSON> <EMAIL>
 */
import {
  GetMapping,
  PostMapping,
  RequestMapping,
  RestController
} from "@tiger/boot";
import { AjaxResult, QueryContext, RequestContext } from "@tiger/core";
import { CodeService } from "./code.service";
import { CodeSubmitVO, SendMessageInfoVO } from "./vo/inputTrigger.vo";
import { getErrorMessage } from "../../../util/util";
import { appLogger } from "@tiger/logger";
import { getWaitingMap } from "./open/consts";

/**
 * 格式化手机号，处理+86前缀
 * @param phone 原始手机号
 * @returns 格式化后的11位手机号
 */
function formatPhoneNumber(phone: string): string {
  if (!phone) return phone;

  // 移除所有空格和特殊字符，只保留数字和+号
  let cleanPhone = phone.replace(/[^\d+]/g, '');

  // 处理+86前缀的情况
  if (cleanPhone.indexOf('+86') === 0) {
    cleanPhone = cleanPhone.substring(3);
  } else if (cleanPhone.indexOf('86') === 0 && cleanPhone.length === 13) {
    // 处理86开头的13位数字（86 + 11位手机号）
    cleanPhone = cleanPhone.substring(2);
  }

  // 验证是否为11位数字且以1开头
  if (/^1\d{10}$/.test(cleanPhone)) {
    return cleanPhone;
  }

  // 如果格式不正确，返回原始值并记录警告
  appLogger.warn(`手机号格式异常，无法格式化：${phone} -> ${cleanPhone}`);
  return phone;
}

@RestController
@RequestMapping("")
export class CodeController {
  constructor(private codeService: CodeService) { }

  /**
   * 提交填写的验证码
   */
  @PostMapping("/submitCode", [])
  async submitCode(ctx: RequestContext<CodeSubmitVO, AjaxResult<boolean>>) {
    const { request } = ctx;
    const { body } = request;
    if (!body || !body?.key || !body?.code) {
      ctx.body = AjaxResult.badRequest("参数错误");
      appLogger.error(`提交验证码参数错误：${JSON.stringify(body)}`);
      return;
    }
    try {
      const result = await this.codeService.submitCode(body);
      if (!result) {
        throw new Error("");
      }
      ctx.body = AjaxResult.success(true);
      appLogger.info(`提交验证码成功：${JSON.stringify(body)}`);
    } catch (error) {
      ctx.body = AjaxResult.fail(500, getErrorMessage(error) || '提交失败');
      appLogger.error(`提交验证码失败：${JSON.stringify({ ...body, errorMessage: getErrorMessage(error) })}`);
    }
  }

  /**
   * 清除验证码缓存数据
   */
  @GetMapping("/clearCodeMap", [])
  async clearCodeMap(ctx: QueryContext<null, AjaxResult<string>>) {
    try {
      const result = await this.codeService.clearCodeMap();
      ctx.body = AjaxResult.success(result);
      appLogger.info(`清除数据成功：${result}`);
    } catch (error) {
      ctx.body = AjaxResult.fail(500, getErrorMessage(error) || '清除失败');
    }
  }


  /**
   * 接收短信转发器的消息
   */
  @PostMapping("/sendMessage", [])
  async sendMessage(ctx: RequestContext<SendMessageInfoVO, AjaxResult<boolean>>) {
    const { body } = ctx.request;
    if (!body || !body?.phone) {
      ctx.body = AjaxResult.badRequest("手机号码不能为空");
      appLogger.error(`手机号码不能为空${JSON.stringify(body)}`);
      return;
    }
    if (!body || !body?.text?.content) {
      ctx.body = AjaxResult.badRequest("短信信息为空");
      appLogger.error(`短信信息为空${JSON.stringify(body)}`);
      return;
    }
    if (!body || !body?.msgtype || body?.msgtype !== 'text') {
      appLogger.warn(`无效短信信息：短信内容不是文本格式${JSON.stringify(body)}`);
      ctx.body = AjaxResult.success(true);
      return;
    }

    // 格式化手机号，处理+86前缀等情况
    const formattedPhone = formatPhoneNumber(body.phone);
    appLogger.info(`手机号格式化：${body.phone} -> ${formattedPhone}`);

    const waitingMap = await getWaitingMap();
    appLogger.info(`等待验证码的keys：${JSON.stringify([...waitingMap.keys()])}`)
    await this.codeService.analyzeSms(body.text.content, formattedPhone);
    appLogger.info(`接收短信转发器的消息成功：${JSON.stringify({ ...body, phone: formattedPhone })}`);
    ctx.body = AjaxResult.success(true);
  }
}