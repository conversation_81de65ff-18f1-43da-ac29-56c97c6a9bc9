/*
 * @Date: 2024-01-04 15:28:38
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-29 16:24:21
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/code.service.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import { Service } from "@tiger/boot";
import { OpenCodeService } from "./open/openCode.service";
import { CodeSubmitVO } from "./vo/inputTrigger.vo";
import { appLogger } from "@tiger/logger";
import { getErrorMessage } from "../../../util/util";

@Service
export class CodeService {
  constructor(
    private openCodeService: OpenCodeService
  ) { }

  async submitCode(params: CodeSubmitVO): Promise<boolean> {
    const { code, key } = params;
    try {
      const result = await this.openCodeService.setCodeInput(key, code);
      if (result) {
        return true;
      } else {
        throw new Error('保存失败');
      }
    } catch (error) {
      appLogger.error(`保存验证码错误：${getErrorMessage(error)}`);
      return false;
    }
  }

  /**
   * 格式化发件时间
   * 处理多种时间格式：
   * 1. 标准斜线格式: 2025/01/01 11:11:11
   * 2. 横线格式: 2025-01-01 11:11:11 (转换为斜线格式)
   * 3. 时间戳格式: 1735689071 或 1735689071000
   */
  private formatSendTime(content: string): string {
    // 首先尝试匹配标准时间格式
    const standardTimeMatch = content.match(/发件时间:\s?(\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2})/);
    if (standardTimeMatch && standardTimeMatch[1]) {
      return standardTimeMatch[1];
    }

    // 尝试匹配横线分隔的时间格式 YYYY-MM-DD HH:mm:ss，并转换为斜线格式
    const dashTimeMatch = content.match(/发件时间:\s?(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/);
    if (dashTimeMatch && dashTimeMatch[1]) {
      const dashTime = dashTimeMatch[1];
      // 将横线替换为斜线
      const slashTime = dashTime.replace(/-/g, '/');
      appLogger.info(`时间格式转换：${dashTime} -> ${slashTime}`);
      return slashTime;
    }

    // 尝试匹配时间戳格式（可能在发件时间后面）
    const timestampMatch = content.match(/发件时间:\s?(\d{10,13})/);
    if (timestampMatch && timestampMatch[1]) {
      const timestamp = timestampMatch[1];
      try {
        // 处理10位时间戳（秒）或13位时间戳（毫秒）
        const timestampNum = parseInt(timestamp);
        const date = timestamp.length === 10 ? new Date(timestampNum * 1000) : new Date(timestampNum);

        // 格式化为 YYYY/MM/DD HH:mm:ss
        const year = date.getFullYear();
        const month = ('0' + (date.getMonth() + 1)).slice(-2);
        const day = ('0' + date.getDate()).slice(-2);
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);
        const seconds = ('0' + date.getSeconds()).slice(-2);

        const formattedTime = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
        appLogger.info(`时间戳转换：${timestamp} -> ${formattedTime}`);
        return formattedTime;
      } catch (error) {
        appLogger.warn(`时间戳转换失败：${timestamp}, 错误：${getErrorMessage(error)}`);
      }
    }

    // 如果都不匹配，返回空字符串
    return '';
  }

  // 解析短信内容
  async analyzeSms(content: string,phone:string){
      const channel = (content.match(/【(.*?)】/) || [])[1] || '';
      const code = (content.match(/验证码为(\d+)/) || [])[1] ||(content.match(/验证码:(\d+)/) || [])[1] || (content.match(/验证码：(\d+)/) || [])[1]  ||(content.match(/验证码(\d+)/) || [])[1] || (content.match(/(\d+)/) || [])[1];

      const sendTime = this.formatSendTime(content);
      appLogger.info(`接收短信信息：电话${phone}、渠道：${channel}、验证码：${code}、发件时间：${sendTime}`);
      if (channel&&code&&sendTime) {
        await this.openCodeService.setCodeByChannel(phone,sendTime,channel,code);
      } else {
        appLogger.warn(`无效短信信息：${getErrorMessage(content)}`);
      }
  }

  // 清除验证码数据
  async clearCodeMap(){
    return this.openCodeService.clearCodeMap();
  }
}