/*
 * @Date: 2024-01-03 15:36:31
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-04 10:07:12
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/open/openCode.service.ts
 * @Description: 
 * @Author: Lamwolff <EMAIL>
 */
import { Service } from "@tiger/boot";
import { GetInputVO, InputInnerTriggerVO, InputTriggerVO } from "../vo/inputTrigger.vo";
import aixos from "@tiger/request";
import FormData from "form-data";
import { ConsulService } from "../../../consul/consul.service";
import { getErrorMessage } from "../../../../util/util";
import { appLogger } from "@tiger/logger";
import { fillMultiMsg } from "./util";
import { generateMsgId } from "../../notify/util";
import { getChannelTag } from "../../../../apolloy";
import * as config from "../../../../conf";
import { deleteChannelCodeMap, deleteWaitingMap, getChannelCodeMap, getWaitingMap, setChannelCodeMap, setWaitingMap, initRedisClient } from "./consts";
import { RedisClientService } from '../../../../redis';

@Service
export class OpenCodeService {
  private redisClient: RedisClientService;

  // Redis缓存键前缀
  private readonly CODE_MAP_PREFIX = 'codeMap:';
  private readonly TIMER_MAP_PREFIX = 'timerMap:';

  constructor(
    private consulService: ConsulService,
    redisClient: RedisClientService
  ) {
    this.redisClient = redisClient;
    // 初始化consts中的Redis缓存服务
    initRedisClient(redisClient);
  }
  

  async codeInputTrigger(params: InputTriggerVO): Promise<boolean> {
    const { phone, key,message} = params;
    // 用于打印日志用的变量，要与formData创建的内容保持一致
    const mobileMsgParams = {
      phone: String(phone),
      message: message,
      level: 3,
      uid: '123',
      topic: 'rpa-sms-code'
    }
    const mobileMsgData = new FormData();
    mobileMsgData.append('phone', String(phone));
    mobileMsgData.append('message', message);
    mobileMsgData.append('level', '3');
    mobileMsgData.append('uid', '123');
    mobileMsgData.append('topic', 'rpa-sms-code');
    try {
      appLogger.info(`发送短信：${JSON.stringify({ ...mobileMsgParams, url: `${this.consulService.getUasServiceUrl()}/push/sms/yanxuan-rpa-common-server` })}`)
      const formHeaders = mobileMsgData.getHeaders();
      const { data: msgResult } = await aixos.post(
        `${this.consulService.getUasServiceUrl()}/push/sms/yanxuan-rpa-common-server`,
        mobileMsgData,
        {
          headers: {
            ...formHeaders
          }
        }
      )
      if (msgResult?.code !== 200) {
        appLogger.error(`发送短信失败：${JSON.stringify(msgResult)}`)
        throw new Error(msgResult.data.errorCode || '发送短信失败');
      }
      appLogger.info(`发送短信成功：${JSON.stringify(msgResult)}`)
    } catch (error) {
      appLogger.error(`发送短信失败：${JSON.stringify(getErrorMessage(error))}`)
      throw new Error(getErrorMessage(error) || '发送短信失败');
    }
    await this.redisClient.set(`${this.CODE_MAP_PREFIX}${key}`, 'initialValue');
    return true;
  }

  async codeInputInnerTrigger(params: InputInnerTriggerVO): Promise<any> {
    const { msgMethod, uids, key, phone } = params;
    try {
      const msgParams: any = {}
      const messageId = generateMsgId('MultiMsg');
      msgMethod.forEach(item => {
        if (item === 'email') {
          msgParams['emailMsg'] = fillMultiMsg(
            item,
            `您好，因严选RPA自动化执行机器人需要重新登录渠道后台，请您收到该平台给您手机号【${phone ? phone : '未填写'}】发送的短信验证码后，及时将验证码填写到如下链接${config.domain}/base/#/codeInput?phone=${phone}&key=${key}，以保障机器人继续自动执行，非常感谢！`,
            uids,
            messageId,
            '【RPA退货地址维护】异常登录验证码填写提醒'
          )
        } else {
          msgParams[`${item}Msg`] = fillMultiMsg(
            item,
            `您好，因严选RPA自动化执行机器人需要重新登录渠道后台，请您收到该平台给您手机号【${phone ? phone : '未填写'}】发送的短信验证码后，及时将验证码填写到如下链接${config.domain}/base/#/codeInput?phone=${phone}&key=${key}，以保障机器人继续自动执行，非常感谢！`,
            uids,
            messageId
          )
        }
      })
      await this.redisClient.set(`${this.CODE_MAP_PREFIX}${key}`, 'initialValue');
      return msgParams;
    } catch (error) {
      appLogger.error(`生成消息内容报错：${JSON.stringify({ data: params, error: getErrorMessage(error) })}`)
      throw new Error(getErrorMessage(error) || '生成消息内容报错');
    }
  }

  async getCodeInput(params: GetInputVO): Promise<string> {
    const { key } = params;
    try {
      const code = await this.redisClient.get(`${this.CODE_MAP_PREFIX}${key}`);
      // 如果获取到的值是初始值，则说明该验证码未填写，直接返回空字符串
      if (code === 'initialValue') {
        return '';
      } else if (typeof code === 'string' && code !== 'initialValue') {
        // 如果验证码已经填写，则从Redis中删除该键值对，并返回该验证码
        // 为防止过快操作导致接口返回异常引起用户误解，延缓验证码删除时间，留一个缓冲期
        // 如果该键值对不存在定时器，则创建一个新的定时器
        const timerKey = `${this.TIMER_MAP_PREFIX}${key}`;
        const hasTimer = await this.redisClient.has(timerKey);
        if (!hasTimer) {
          // 设置定时器标记，300秒后过期
          await this.redisClient.set(timerKey, 'timer', 300);
          // 设置验证码5分钟后过期
          await this.redisClient.setWithExpireAt(`${this.CODE_MAP_PREFIX}${key}`, code, Date.now() + 300000);
        }
        // 返回验证码，如果不存在则返回空字符串
        return code || '';
      } else {
        // 如果填写的是非法的值，则直接删除该键值对，并返回空字符串
        await this.redisClient.delete(`${this.CODE_MAP_PREFIX}${key}`);
        return '';
      }
    } catch (error) {
      throw new Error(getErrorMessage(error) || '获取验证码报错');
    }
  }

  async setCodeInput(key: string, code: string): Promise<boolean> {
    try {
      const exists = await this.redisClient.has(`${this.CODE_MAP_PREFIX}${key}`);
      if (exists) {
        await this.redisClient.set(`${this.CODE_MAP_PREFIX}${key}`, code);
        return true;
      } else {
        const keys = await this.redisClient.keys(`${this.CODE_MAP_PREFIX}*`);
        const cleanKeys = keys.map(k => k.replace(this.CODE_MAP_PREFIX, ''));
        appLogger.error(`验证码填写需求不存在：${key}, 现有的key: ${JSON.stringify(cleanKeys)}`)
        return false;
      }
    } catch (error) {
      throw new Error(getErrorMessage(error) || '设置验证码报错');
    }
  }
  async setCodeByChannel(phone: string, sendTime: string, channel: string, code: string) {
    const channelTag = getChannelTag();
    const _sendTime = new Date(sendTime).getTime();
    const nowTime = Date.now();
    const key = `${phone}-${channelTag[channel]}-${_sendTime}-${nowTime}`;
    await setChannelCodeMap(key, {code: code, isMatch: false});
    // 匹配等待验证码key
    const waitKeyPrefix = `${phone}-${channelTag[channel]}-`;
    const waitingMap = await getWaitingMap();
    appLogger.info(`等待验证码的keys：${JSON.stringify([...waitingMap.keys()])}, code: ${code}`)

    const targetList: string[] = [];
    for (const [waitKey, waitValue] of waitingMap.entries()) {
      const [,, tiggerTime] = waitKey.split('-');
      if (waitKey.startsWith(waitKeyPrefix) && !waitValue?.isTarget && Number(tiggerTime) + 60000 > nowTime) {
        targetList.push(waitKey);
      }
    }

    if (targetList.length === 0) {
      // 存储验证码信息到channelCodeMap中
      await setChannelCodeMap(key, {code: code, isMatch: false});
      appLogger.warn(`验证码未匹配的等待验证码的key：${key}, code: ${code}`)
    } else if (targetList.length === 1) {
      // 存储验证码信息到channelCodeMap中
      await setChannelCodeMap(key, {code: code, isMatch: true});
      // 如果存在一个等待验证码key，则设置该键值
      const targetKey = targetList[0];
      await setWaitingMap(targetKey, {code: code, isTarget: true});
    } else {
      appLogger.warn(`验证码匹配到多个等待验证码的key：${JSON.stringify(targetList)}, code: ${code}`);
      // 存储验证码信息到channelCodeMap中
      await setChannelCodeMap(key, {code: code, isMatch: true});
      // 将验证码设置给最早等待验证码的key
      const targetKeyTime = targetList.map(key => {
        const [,, time] = key.split('-');
        return Number(time)
      }).sort((a, b) => a - b)[0];
      const targetKey = `${waitKeyPrefix}${targetKeyTime}`;
      await setWaitingMap(targetKey, {code: code, isTarget: true});
    }
  }
  // 手动触发
  async getCodeByWaitKey(waitKey: string): Promise<string> {
    const waitingMap = await getWaitingMap();
    appLogger.info(`现有等待验证码的keys：${JSON.stringify([...waitingMap.keys()])}`)
    // 如果存在等待验证码的key，则返回该键值
    if (waitingMap.has(waitKey)) {
      return waitingMap.get(waitKey)?.code || '';
    } else {
      appLogger.warn(`等待验证码的key不存在：${waitKey}`)
      return '-1';
    }
  }
  
  // 自动触发
  async getCodeByTiggerTime(phone: string, channel: string, tiggerTime: number): Promise<string> {
    const channelTag = getChannelTag();
    const targetKey = `${phone}-${channelTag[channel]}-${tiggerTime}`;
    const waitingMap = await getWaitingMap();

    // 如果存在等待验证码的key，则返回该键值
    if (waitingMap.has(targetKey)) {
      appLogger.info(`存在等待验证码的key：${targetKey},target:${JSON.stringify(waitingMap.has(targetKey))}`)
      return waitingMap.get(targetKey)?.code || '';
    } else {
      const channelCodeMap = await getChannelCodeMap();
      appLogger.info(`getChannelCodeMap：${JSON.stringify([...channelCodeMap.keys()])}`)

      const targetList: string[] = [];
      for (const [key, value] of channelCodeMap.entries()) {
        const [targatPhone, targatChannel, , time] = key.split('-');
        const isMatch = value?.isMatch;
        appLogger.info(`${key}:isMatch=${isMatch}`)
        if (targatPhone == phone && targatChannel == channelTag[channel] && Number(time) >= tiggerTime && !isMatch) {
          targetList.push(key);
        }
      }

      appLogger.info(`命中的keys：${JSON.stringify(targetList)}`)
      if (targetList.length) {
        const key = targetList[0];
        const target = channelCodeMap.get(key);
        appLogger.info(`无需注册，命中key${key},target:${JSON.stringify(target)}`)
        if (target) {
          await setChannelCodeMap(key, {code: target.code, isMatch: true});
          return target.code || '';
        }
      } else {
        appLogger.info(`注册等待验证码的key${targetKey}`);
        // 注册等待验证码的key
        await setWaitingMap(targetKey, {code: '', isTarget: false});
      }
    }
    const updatedWaitingMap = await getWaitingMap();
    appLogger.info(`等待验证码的keys：${JSON.stringify([...updatedWaitingMap.keys()])}`)
    return '';
  }

  async checkTiggerCodeLock(phone: string, channel: string): Promise<string> {
    const channelTag = getChannelTag();
    const keyPrefix = `${phone}-${channelTag[channel]}-`;
    const waitingMap = await getWaitingMap();
    appLogger.info(`等待验证码的keys：${JSON.stringify([...waitingMap.keys()])}`)

    // 获取等待一分钟以内的等待验证码的key
    const targetList: string[] = [];
    for (const [key, value] of waitingMap.entries()) {
      const [,, tiggerTime] = key.split('-');
      if (key.startsWith(keyPrefix) && !value?.isTarget && Number(tiggerTime) + 70000 > Date.now()) {
        targetList.push(key);
      }
    }

    if (targetList.length) {
      appLogger.info(`存在等待一分钟以内的等待验证码的keys：${JSON.stringify(targetList)}`)
      return '';
    } else {
      // 注册等待验证码key
      const waitKey = `${phone}-${channelTag[channel]}-${Date.now()}`;
      await setWaitingMap(waitKey, {code: '', isTarget: false});
      const updatedWaitingMap = await getWaitingMap();
      appLogger.info(`等待验证码的keys：${JSON.stringify([...updatedWaitingMap.keys()])}`)
      return waitKey;
    }
  }

  // 清除验证码相关数据
  async clearCodeMap() {
    const nowTime = Date.now();
    const waitingMap = await getWaitingMap();
    const channelCodeMap = await getChannelCodeMap();

    // 清理过期的等待验证码
    for (const key of waitingMap.keys()) {
      const [,, time] = key.split('-');
      // 如果等待时间超过两小时，则删除该键值
      if (Number(time) + 7200000 < nowTime) {
        await deleteWaitingMap(key);
      }
    }

    // 清理过期的渠道验证码
    for (const key of channelCodeMap.keys()) {
      const [,,, time] = key.split('-');
      // 如果等待时间超过两小时，则删除该键值
      if (Number(time) + 7200000 < nowTime) {
        await deleteChannelCodeMap(key);
      }
    }

    const updatedWaitingMap = await getWaitingMap();
    const updatedChannelCodeMap = await getChannelCodeMap();
    return `WaitingMapKeys:${JSON.stringify([...updatedWaitingMap.keys()])};ChannelCodeMapkeys:${JSON.stringify([...updatedChannelCodeMap.keys()])}`;
  }
}
