/*
 * @Date: 2024-01-29 15:04:54
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-29 15:39:28
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/open/consts.ts
 * @Description: 一些常量
 * @Author: <PERSON>wolf<PERSON> <EMAIL>
 */

export const MSG_INFO_MAPS = {
  'feishu': {
    content: '飞书消息内容',
    level: 1,
    messageId: '',
    uids: [] as string[],
    subject: '' // 非邮件渠道不填写该字段，这里只为通过ts类型校验
  },
  'popo': {
    content: 'popo消息内容',
    level: 1,
    messageId: '',
    uids: [] as string[],
    subject: '' // 非邮件渠道不填写该字段，这里只为通过ts类型校验
  },
  'email': {
    content: '邮箱消息内容',
    level: 1,
    messageId: '',
    subject: '',
    uids: [] as string[]
  }
}


import { RedisClientService } from '../../../../redis';

// Redis缓存服务实例
let redisClient: RedisClientService;

// 初始化Redis缓存服务
export function initRedisClient(cacheService: RedisClientService) {
  redisClient = cacheService;
}

// 缓存键前缀
const CHANNEL_CODE_PREFIX = 'channelCode:';
const WAITING_CODE_PREFIX = 'waitingCode:';

export async function setChannelCodeMap(key: string, value: {code: string; isMatch: boolean}): Promise<boolean> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  return await redisClient.set(`${CHANNEL_CODE_PREFIX}${key}`, value);
}

export async function deleteChannelCodeMap(key: string): Promise<boolean> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  return await redisClient.delete(`${CHANNEL_CODE_PREFIX}${key}`);
}

export async function getChannelCodeMap(): Promise<Map<string, {code: string; isMatch: boolean}>> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  const keys = await redisClient.keys(`${CHANNEL_CODE_PREFIX}*`);
  const map = new Map<string, {code: string; isMatch: boolean}>();

  for (const fullKey of keys) {
    const key = fullKey.replace(CHANNEL_CODE_PREFIX, '');
    const value = await redisClient.get(fullKey);
    if (value) {
      map.set(key, value);
    }
  }

  return map;
}

export async function getChannelCodeMapValue(key: string): Promise<{code: string; isMatch: boolean} | null> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  return await redisClient.get(`${CHANNEL_CODE_PREFIX}${key}`);
}

export async function setWaitingMap(key: string, value: {code: string; isTarget: boolean}): Promise<boolean> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  return await redisClient.set(`${WAITING_CODE_PREFIX}${key}`, value);
}

export async function deleteWaitingMap(key: string): Promise<boolean> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  return await redisClient.delete(`${WAITING_CODE_PREFIX}${key}`);
}

export async function getWaitingMap(): Promise<Map<string, {code: string; isTarget: boolean}>> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  const keys = await redisClient.keys(`${WAITING_CODE_PREFIX}*`);
  const map = new Map<string, {code: string; isTarget: boolean}>();

  for (const fullKey of keys) {
    const key = fullKey.replace(WAITING_CODE_PREFIX, '');
    const value = await redisClient.get(fullKey);
    if (value) {
      map.set(key, value);
    }
  }

  return map;
}

export async function getWaitingMapValue(key: string): Promise<{code: string; isTarget: boolean} | null> {
  if (!redisClient) {
    throw new Error('Redis缓存服务未初始化');
  }
  return await redisClient.get(`${WAITING_CODE_PREFIX}${key}`);
}
