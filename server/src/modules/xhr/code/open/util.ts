/*
 * @Date: 2024-01-29 15:28:25
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-29 16:14:22
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/open/util.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

import { MSG_INFO_MAPS } from "./consts"

type MsgType = 'feishu' | 'popo' | 'email'

export const fillMultiMsg = (msgType: MsgType, msgContent: string, uids: string[], messageId: string, subject?: string): any => {
  const curMsgParam = MSG_INFO_MAPS[msgType]
  curMsgParam.content = msgContent
  curMsgParam.uids = uids
  curMsgParam.messageId = messageId
  if (subject && msgType === 'email') {
    curMsgParam.subject = subject
  }
  return curMsgParam
}