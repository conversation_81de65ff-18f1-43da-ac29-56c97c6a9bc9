/*
 * @Date: 2024-01-03 15:35:05
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 15:32:43
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/open/openCode.controller.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */

import {
  PostMapping,
  RequestMapping,
  RestController
} from "@tiger/boot";
import { OpenCodeService } from './openCode.service';
import { AjaxResult, RequestContext } from "@tiger/core";
import { appLogger } from "@tiger/logger";
import { MultiMsgResult, NotifyService, SingleMsgResult } from "@eagle/common-service-node";

import { GetCodeByWaitKeyVO, GetCodeVO, GetInputVO, InputInnerTriggerVO, InputTriggerVO, TiggerCodeCheckVO } from "../vo/inputTrigger.vo";
import { getErrorMessage } from "../../../../util/util";
import { validSign } from "../../../../middlewares/sign.middleware";


@RestController
@RequestMapping("/open", [validSign])
export class OpenCodeController {
  constructor(
    private openCodeService: OpenCodeService,
    private notifyService: NotifyService,
  ) { }

  /**
   * 发起填写验证码接口
   */
  @PostMapping('/inputTrigger', [])
  async inputTrigger(
    ctx: RequestContext<InputTriggerVO, AjaxResult<boolean>>
  ) {
    const { request } = ctx;
    const { body } = request;
    if (!body || !body?.phone || !body?.key || !body?.message) {
      ctx.body = AjaxResult.badRequest('参数错误');
      appLogger.error(`发起验证码参数错误：${JSON.stringify(body)}`);
      return;
    }
    try {
      const result = await this.openCodeService.codeInputTrigger(body);
      if (!result) {
        throw new Error('');
      }
      ctx.body = AjaxResult.success(true);
      appLogger.info(`发起验证码成功：${JSON.stringify(body)}`);
    } catch (error) {
      ctx.body = AjaxResult.fail(500, '发起验证码失败');
      appLogger.error(`发起验证码失败：${JSON.stringify({ ...body, errorMessage: getErrorMessage(error) })}`);
    }
  }

  /**
   * 获取验证码填写结果
   */
  @PostMapping('/getInput', [])
  async getInput(
    ctx: RequestContext<GetInputVO, AjaxResult<string>>
  ) {
    const { request } = ctx;
    const { body } = request;
    if (!body || !body?.key) {
      ctx.body = AjaxResult.badRequest('参数错误');
      appLogger.error(`获取验证码参数错误：${JSON.stringify(body)}`);
      return;
    }
    try {
      const result = await this.openCodeService.getCodeInput(body);
      if (!result) {
        ctx.body = AjaxResult.success('');
      } else {
        ctx.body = AjaxResult.success(result);
      }
      appLogger.info(`获取验证码：${JSON.stringify({ ...body, result })}`)
    } catch (error) {
      ctx.body = AjaxResult.fail(500, getErrorMessage(error));
      appLogger.error(`获取验证码失败：${JSON.stringify({ ...body, errorMessage: getErrorMessage(error) })}`);
    }
  }

  /**
   * 发起填写验证码接口，消息通知为内部消息通道（飞书、popo和邮件等）
   */
  @PostMapping('/inputInnerTrigger', [])
  async inputInnerTrigger(ctx: RequestContext<InputInnerTriggerVO, AjaxResult<MultiMsgResult | SingleMsgResult>>) {
    const { request } = ctx;
    const { body } = request;
    if (!body || !body.phone || !body?.uids || (body.uids?.length < 1) || !body?.key || !body.msgMethod || (body.msgMethod?.length < 1)) {
      ctx.body = AjaxResult.badRequest('参数错误');
      appLogger.error(`发起验证码参数错误：${JSON.stringify(body)}`);
      return;
    }
    let msgParams: any;
    try {
      msgParams = await this.openCodeService.codeInputInnerTrigger(body);
      appLogger.info(`获取发起消息通知参数成功：${JSON.stringify(msgParams)}，接收短信的手机号：${body.phone}`)
    } catch (error) {
      ctx.body = AjaxResult.fail(500, '发起失败');
      appLogger.error(`获取发起消息通知参数失败：${JSON.stringify({ ...body, errorMessage: getErrorMessage(error) })}`);
      return;
    }
    try {
      const notifyResult = await this.notifyService.notify('multi', msgParams)
      ctx.body = AjaxResult.success(notifyResult);
    } catch (error) {
      ctx.body = AjaxResult.fail(500, '发起失败');
      appLogger.error(`发起验证码失败：${JSON.stringify({ ...body, errorMessage: getErrorMessage(error) })}`);
    }
  }

  /**
   * 获取自动触发的验证码
   */
  @PostMapping('/getCodeByTiggerTime', [])
  async getCodeByTiggerTime(
    ctx: RequestContext<GetCodeVO, AjaxResult<string>>
  ) {
    const { body } = ctx.request;
    if (!body || !body.phone || !body.channel || !body.tiggerTime) {
      appLogger.error(`参数错误${JSON.stringify(body)}`);
      ctx.body = AjaxResult.badRequest('参数错误');
      return;
    }
    const result = await this.openCodeService.getCodeByTiggerTime(body.phone, body.channel, body.tiggerTime);
    appLogger.info(`获取验证码：${JSON.stringify({ ...body, result })}`)
    ctx.body = AjaxResult.success(result);
  }

  /**
 * 获取手动触发的验证码
 */
  @PostMapping('/getCodeByWaitKey', [])
  async getCodeByWaitKey(
    ctx: RequestContext<GetCodeByWaitKeyVO, AjaxResult<string>>
  ) {
    const { body } = ctx.request;
    if (!body || !body.waitKey) {
      appLogger.error(`参数错误${JSON.stringify(body)}`);
      ctx.body = AjaxResult.badRequest('参数错误');
      return;
    }
    const result = await this.openCodeService.getCodeByWaitKey(body.waitKey);
    if (result === '-1') {
      ctx.body = AjaxResult.fail(500, `等待验证码的key不存在：${body.waitKey}`);
    } else {
      ctx.body = AjaxResult.success(result);
    }
  }

  /**
   * 查询是否可以触发验证码
   */
  @PostMapping('/tiggerCode/check', [])
  async checkTiggerCode(
    ctx: RequestContext<TiggerCodeCheckVO, AjaxResult<string>>
  ) {
    const { body } = ctx.request;
    if (!body || !body.phone || !body.channel) {
      appLogger.error(`参数错误${JSON.stringify(body)}`);
      ctx.body = AjaxResult.badRequest('参数错误');
      return;
    }
    const result = await this.openCodeService.checkTiggerCodeLock(body.phone, body.channel);
    appLogger.info(`获取验证码：${JSON.stringify({ ...body, result })}`)
    ctx.body = AjaxResult.success(result);
  }
}
