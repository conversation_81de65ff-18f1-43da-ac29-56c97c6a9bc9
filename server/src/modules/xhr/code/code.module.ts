/*
 * @Date: 2024-01-03 15:34:49
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-04 15:48:32
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/code/code.module.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

import { CodeController } from './code.controller';
import { OpenCodeController } from './open/openCode.controller';
import { TgModule } from '@tiger/boot';

@TgModule({
  prefix: '/code',
  controllers: [OpenCodeController, CodeController]
})
export class CodeModule { }
