/*
 * @Date: 2024-01-08 16:26:51
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-22 14:50:50
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/nosUpload/nosUpload.controller.ts
 * @Description:
 * @Author: Lamwolff <EMAIL>
 */
import { PostMapping, RequestMapping, RestController } from "@tiger/boot";
import { AjaxResult, FileContext, RequestContext } from "@tiger/core";

import { Stream } from "stream";
import { validSign } from "../../../middlewares/sign.middleware";
import { appLogger } from "@tiger/logger";
import { getErrorMessage } from "../../../util/util";
import {
  FmsService,
  IFileInfo,
  IFilesDownloadOpt,
} from "@eagle/common-service-node";

@RestController
@RequestMapping("/file")
export class FileController {
  constructor(private fmsService: FmsService) {}

  /**
   * @description 上传文件
   * @param {RequestContext<IFileUploadOpt, AjaxResult<IFileInfo>>} ctx
   * @returns {void}
   */
  @PostMapping("/upload")
  async uploadFile(ctx: FileContext<any, AjaxResult<IFileInfo>>) {
    const { request } = ctx;
    const { files,body } = request;
    if (!files) {
        return ctx.body = AjaxResult.badRequest('参数不全');
    }
    if (!body) {
        return ctx.body = AjaxResult.badRequest('缺少参数');
    }
    const { topic } = body;
    const file = files.file as any;
    try {
      const result = await this.fmsService.uploadFile({
        file,
        topic: topic||"rpa-file",
      });
      ctx.body = result;
    } catch (error) {
      appLogger.error(
        `上传失败：${JSON.stringify({ errorMessage: getErrorMessage(error) })}`
      );
      ctx.body = AjaxResult.fail(500, "上传失败");
    }
  }

  /**
   * @description 下载文件
   * @param {RequestContext<IFilesDownloadOpt, AjaxResult<IFileInfo>>} ctx
   * @returns {void}
   */
  @PostMapping("/downLoad", [validSign])
  async downLoadFile(ctx: RequestContext<IFilesDownloadOpt, Stream | string>) {
    const { request } = ctx;
    const { body } = request;
    if (!body) {
      ctx.body = "参数错误";
      return;
    }
    try {
      const result = await this.fmsService.downLoadFile(body);
      ctx.body = result;
    } catch (error) {
      appLogger.error(
        `上传失败：${JSON.stringify({
          ...body,
          errorMessage: getErrorMessage(error),
        })}`
      );
      ctx.body = "上传失败";
    }
  }
}
