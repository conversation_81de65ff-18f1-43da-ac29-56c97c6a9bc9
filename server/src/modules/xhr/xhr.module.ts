/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-27 11:35:43
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/xhr.module.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import { TgModule } from '@tiger/boot';
import { UserModule } from './user/user.module';
import { TenantModule } from './tenant/tenant.module';
import { WorkflowModule } from './workflow/workflow.module';
import { CodeModule } from './code/code.module';
import { NosUploadModule } from './nosUpload/nosUpload.module';
import { ChannelModule } from './channel/channel.module';
import { PddModule } from './pdd/pdd.module';
import { NotifyModule } from './notify/notify.module';
import { FileModule } from './file/file.module';
import { GPTModule } from './gpt/gpt.module';

@TgModule({
  prefix: `/xhr`,
  imports: [UserModule, TenantModule, WorkflowModule, CodeModule, NosUploadModule, ChannelModule, PddModule, NotifyModule,FileModule,GPTModule],
  controllers: []
})
export class XhrModule { }
