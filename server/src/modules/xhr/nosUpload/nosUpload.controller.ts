/*
 * @Date: 2024-01-08 16:26:51
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-22 14:50:50
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/nosUpload/nosUpload.controller.ts
 * @Description: 
 * @Author: Lamwolff <EMAIL>
 */
import {
  GetMapping,
  PostMapping,
  RequestMapping,
  RestController
} from "@tiger/boot";
import { AjaxResult, RequestContext, FileContext } from "@tiger/core";
import { NosUploadVO } from "./vo/nosUpload.vo";
import { validSign } from "../../../middlewares/sign.middleware";
import { NosUploadService } from "./nosUpload.service";
import { appLogger } from "@tiger/logger";
import { getErrorMessage } from "../../../util/util";
import { createReadStream } from 'fs';

@RestController
@RequestMapping("")
export class NosUploadController {
  constructor(
    private nosUploadService: NosUploadService
  ) { }

  /**
   * 
   */
  @PostMapping("/upload", [validSign])
  /**
   * @description: 上传图片接口
   * @param {RequestContext<NosUploadVO, AjaxResult<string>>} ctx
   * @returns {void}
   */
  async upload(ctx: RequestContext<NosUploadVO, AjaxResult<string>>) {
    const { request } = ctx;
    const { body } = request;
    if (!body || !body?.img || !body?.imgName) {
      ctx.body = AjaxResult.badRequest("参数错误");
      return;
    }
    if (body.quality && typeof body.quality !== "number") {
      ctx.body = AjaxResult.badRequest("参数错误");
      return;
    }
    try {
      const url = await this.nosUploadService.uploadImg(body.img, Number((body?.quality)?.toFixed(1)) || 0.5, body.imgName);
      ctx.body = AjaxResult.success(url);
    } catch (error) {
      appLogger.error(`图片上传失败：${JSON.stringify({ ...body, errorMessage: getErrorMessage(error) })}`);
      ctx.body = AjaxResult.fail(500, "上传失败");
    }
  }

  /**
   * 
   */
  @PostMapping("/uploadFile", [validSign])
  /**
   * @description: 上传文件接口
   * @param {FileContext<null, AjaxResult<any>>} ctx
   * @returns {void}
   */
  async uploadFile(ctx: FileContext<null, AjaxResult<any>>) {
    if (!ctx.request.files) {
      ctx.body = AjaxResult.badRequest("参数错误");
      return;
    }
    const file = ctx.request.files.file;

    if (file instanceof Array) {
      ctx.body = AjaxResult.badRequest("暂不支持批量文件");
      return;
    }

    try {
      const url = await this.nosUploadService.uploadFilePromise(createReadStream(file.path), file.name as string);
      ctx.body = AjaxResult.success(url);
    } catch (error) {
      appLogger.error(`文件上传失败：${JSON.stringify({ errorMessage: getErrorMessage(error) })}`);
      ctx.body = AjaxResult.fail(500, "上传失败");
    }
  }
}