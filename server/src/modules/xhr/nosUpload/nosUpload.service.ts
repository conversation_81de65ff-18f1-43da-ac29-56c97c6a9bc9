/*
* @Date: 2024-02-19 15:42:45
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-19 17:42:56
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/nosUpload/nosUpload.service.ts
* @Description: 
* @Author: Lamwolff <EMAIL>
*/
import { Service } from "@tiger/boot";
import { Readable } from "stream";
import FormData from "form-data";
import { getErrorMessage } from "../../../util/util";
import { appLogger } from "@tiger/logger";
import axios from "@tiger/request";
import { ConsulService } from "../../consul/consul.service";

@Service
export class NosUploadService {
  constructor(private consulService:ConsulService) { }
  /**
   * @description: 处理图片数据，并上传到nos桶
   * @param {string} fileDataByBase64 base64编码的图片数据
   * @param {number} quality 图片质量，0.1-1，1代表不压缩，不传则nos服务侧默认为0.8
   * @returns {Promise<string>} nos上传地址
   */
  async uploadImg(fileDataByBase64: string, quality: number, imgName: string): Promise<string> {
    appLogger.info(`图片数据处理开始：${JSON.stringify({ file: fileDataByBase64, quality, imgName })}`)
    const imgBufferStream = new Readable();
    try {
      const imageBuffer = Buffer.from(fileDataByBase64, 'base64');
      imgBufferStream.push(imageBuffer);
      imgBufferStream.push(null);
    } catch (error) {
      appLogger.error(`图片数据处理失败：${error}`)
      throw new Error('图片处理格式错误');
    }
    try {
      const result = await this.uploadPromise(imgBufferStream, quality, imgName);
      return result;
    } catch (error) {
      appLogger.error(`图片上传失败：${error}`)
      throw new Error(getErrorMessage(error) || '图片上传失败');
    }
  }

  private uploadPromise(fileStream: Readable, quality: number, imgName: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const form = new FormData();
      const serverUrl =  `${this.consulService.getStaticServiceUrl()}/xhr/image/consul/uploadForRpa.json`;
      form.append('file', fileStream, imgName || 'image.png');
      if (quality) {
        form.append('quality', quality);
      }
      const formHeaders = form.getHeaders();
      try {
        axios.post(
          serverUrl,
          form,
          {
            headers: {
              ...formHeaders
            }
          }).then(res => {
            resolve(res.data.data[0].url);
          }).catch(err => {
            reject(err);
          })
      } catch (error) {
        appLogger.error(`图片上传nos桶失败：${error}`)
        throw new Error(getErrorMessage(error) || '图片上传失败');
      }
    })
  }

  uploadFilePromise(fileStream: Readable, fileName: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const form = new FormData();
      const serverUrl =  `${this.consulService.getStaticServiceUrl()}/xhr/file/consul/uploadForRpa.json`;
      form.append('file', fileStream, fileName);

      const formHeaders = form.getHeaders();
      try {
        axios.post(
          serverUrl,
          form,
          {
            headers: {
              ...formHeaders
            },
            maxContentLength: 100 * 1024 * 1024
          }).then(res => {
            resolve(res.data.data[0].url);
          }).catch(err => {
            reject(err);
          })
      } catch (error) {
        appLogger.error(`文件上传nos桶失败：${error}`)
        throw new Error(getErrorMessage(error) || '文件上传失败');
      }
    })
  }  
}