/*
 * @Date: 2024-01-19 15:46:27
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 15:21:00
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/channel/channel.service.ts
 * @Description: 
 * @Author: <PERSON>wolf<PERSON> <EMAIL>
 */
import { Service } from "@tiger/boot";
import { RobotMapList, ShopConfigInfo, ShopInfoPO } from "./po/channel.po";
import { ApolloyConfig, RobotListConfig } from "../../../apolloy";
import { getErrorMessage } from "../../../util/util";
import { appLogger } from "@tiger/logger";

type ApolloyKeyForShop = 'JD.config' | 'TX.config' | 'DY.config'

@Service
export class ChannelService {
  constructor() { }
  shopApolloYMap: { [key: string]: ApolloyKeyForShop } = {
    'jd': 'JD.config',
    'tx': 'TX.config',
    'dy': 'DY.config'
  }
  async getShopsFromApolloY(channel: string): Promise<ShopInfoPO[]> {
    let shopConfig: ShopConfigInfo;
    let channelName: ApolloyKeyForShop;
    let shops: ShopInfoPO[];
    try {
      channelName = this.shopApolloYMap[channel];
    } catch (error) {
      appLogger.error(`获取渠道Key错误:${getErrorMessage(error)}`);
      throw new Error(`获取渠道Key错误:${getErrorMessage(error)}`);
    }
    try {
      const shopConfigStr = ApolloyConfig[channelName];
      shopConfig = JSON.parse(shopConfigStr);
    } catch (error) {
      appLogger.error(`获取渠道Apollo配置错误:${getErrorMessage(error)}`);
      throw new Error(`获取渠道Apollo配置错误:${getErrorMessage(error)}`);
    }
    try {
      shops = shopConfig.shops;
    } catch (error) {
      appLogger.error(`获取渠道店铺配置错误:${getErrorMessage(error)}`);
      throw new Error(`获取渠道店铺配置错误:${getErrorMessage(error)}`);
    }
    return shops;
  }

  async getShopsFromRobotList(): Promise<RobotMapList[]> {
    let robotList: RobotMapList[] = [];
    try {
      const robotListStr = RobotListConfig['channelRobotMap']
      robotList = JSON.parse(robotListStr);
    } catch (error) {
      appLogger.error(`获取渠道店铺Apollo配置错误:${getErrorMessage(error)}`);
      throw new Error(`获取渠道店铺Apollo配置错误:${getErrorMessage(error)}`);
    }
    return robotList;
  }
}