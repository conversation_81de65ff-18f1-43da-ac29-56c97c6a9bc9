/*
 * @Date: 2024-01-19 15:44:30
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 14:48:14
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/channel/channel.controller.ts
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */
import { GetMapping, PostMapping, RequestMapping, RestController } from "@tiger/boot";
import { ChannelService } from "./channel.service";
import { AjaxResult, QueryContext } from "@tiger/core";
import { AllShopsVO, QueryShopsVO } from "./vo/channel.vo";
import { appLogger } from "@tiger/logger";
import { encryptResponse, getErrorMessage, matchByAllProperties } from "../../../util/util";
import { ApolloyConfig } from "../../../apolloy";
import { RobotMapList } from "./po/channel.po";

@RestController
@RequestMapping("")
export class ChannelController {
  constructor(private channelService: ChannelService) { }

  @GetMapping("/getShopInfo", [])
  async getShopInfo(ctx: QueryContext<QueryShopsVO, AjaxResult<string>>) {
    const { query } = ctx;
    if (!query || (!query.channel && !query.channelId && !query.accountName && !query.phone && !query.shopAccount && !query.shopName)) {
      ctx.body = AjaxResult.badRequest("参数错误");
      appLogger.error(`获取店铺信息参数错误：${JSON.stringify(query)}`);
      return;
    }
    let shopList: RobotMapList[] = []
    try {
      const allShopList = await this.channelService.getShopsFromRobotList();
      try {
        shopList = allShopList.filter(item => matchByAllProperties({ ...query }, item));
      } catch (error) {
        appLogger.error(`筛选渠道店铺错误：${getErrorMessage(error)}`)
        throw new Error(`筛选渠道店铺错误：${getErrorMessage(error)}`)
      }
      const AESSecret = ApolloyConfig["rpa.aes.appSecret"];
      const AESIv = ApolloyConfig["rpa.aes.iv"];
      const encryptedInfo = encryptResponse(shopList, AESSecret, AESIv);
      ctx.body = AjaxResult.success(encryptedInfo);
      appLogger.info(`获取店铺信息成功：${JSON.stringify({ ...query, shopList })}`);
      return;
    } catch (error) {
      appLogger.error(`获取店铺信息失败：${JSON.stringify({ ...query, errorMessage: getErrorMessage(error) })}`);
      ctx.body = AjaxResult.internal("获取店铺信息失败");
      return;
    }
  }

  @GetMapping("/getAllShop", [])
  async getAllShop(ctx: QueryContext<AllShopsVO, AjaxResult<string>>) {
    const { query } = ctx;
    if (!query || !query.channel) {
      ctx.body = AjaxResult.badRequest("参数错误");
      appLogger.error(`获取店铺信息参数错误：${JSON.stringify(query)}`);
      return;
    }
    const { channel } = query;
    try {
      const shopList = await this.channelService.getShopsFromApolloY(channel);
      if (!shopList) {
        ctx.body = AjaxResult.internal("未找到该店铺");
        return;
      }
      const AESSecret = ApolloyConfig["rpa.aes.appSecret"];
      const AESIv = ApolloyConfig["rpa.aes.iv"];
      const encryptedInfo = encryptResponse(shopList, AESSecret, AESIv);
      ctx.body = AjaxResult.success(encryptedInfo);
      appLogger.info(`获取店铺列表成功：${JSON.stringify({ ...query, shopList })}`);
      return;
    } catch (error) {
      appLogger.error(`获取店铺列表失败：${JSON.stringify({ ...query, errorMessage: getErrorMessage(error) })}`);
      ctx.body = AjaxResult.internal("获取店铺列表失败");
      return;
    }
  }
}