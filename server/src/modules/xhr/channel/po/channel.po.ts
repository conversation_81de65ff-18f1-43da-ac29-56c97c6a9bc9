/*
 * @Date: 2024-01-19 15:59:09
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 14:25:37
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/xhr/channel/po/channel.po.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
export class ShopConfigInfo {
  /** 店铺列表 */
  shops: ShopInfoPO[];
  /** 用户信息 */
  userEmails: UserEmail[]
}

export class ShopInfoPO {
  /** 店铺ID */
  id: number;
  /** 店铺名称 */
  name: string;
  /** 登录账号 */
  account: string;
  /** 登录密码 */
  password: string;
}

export class RobotMapList {
  /** 渠道，现支持的渠道：jd-京东、tx-淘系、dy-抖音 */
  channel: string;
  /** 渠道id */
  channelId: string;
  /** 店铺名称 */
  shopName: string;
  /** 店铺账号 */
  shopAccount: string;
  /** rpa机器人账号 */
  accountName: string;
  /** 用于消息接收的手机号 */
  phone: string;
}

export class UserEmail {
  /** 用户邮箱 */
  email: string;
  /** 用户名 */
  name: string;
}