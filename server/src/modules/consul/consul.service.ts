/*
 * @Date: 2024-01-03 17:13:06
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-01-11 17:56:26
 * @FilePath: /yanxuan-rpa-common-server/server/src/modules/consul/consul.service.ts
 * @Description: 
 * @Author: <PERSON>wolf<PERSON> <EMAIL>
 */

import { Service } from "@tiger/boot";
import { getServiceUrl } from "@tiger/core";

@Service
export class ConsulService {
  private uasServices: any = {
    dev: 'https://rpa.test.you.163.com/base/xhr/uas',
    test: 'http://127.0.0.1:8550/proxy/test.logistics-uas.service.mailsaas',
    regression: 'http://127.0.0.1:8550/proxy/regression.logistics-uas.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.logistics-uas.service.mailsaas'
  }

  private msgServices: any = {
    dev: 'https://rpa.test.you.163.com/base/xhr/msg',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-msg.service.mailsaas',
    regression: 'http://127.0.0.1:8550/proxy/regression2test.yanxuan-msg.service.mailsaas',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-msg.service.mailsaas'
  }
//   const serverUrl = 'http://127.0.0.1:8550/proxy/test.yanxuan-static-service.service.mailsaas/yanxuan-static-service/xhr/image/consul/uploadForRpa.json';
  private staticServices: any = {
    dev: 'https://rpa.test.you.163.com/base/xhr/upload',
    test: 'http://127.0.0.1:8550/proxy/test.yanxuan-static-service.service.mailsaas/yanxuan-static-service',
    regression: 'http://127.0.0.1:8550/proxy/regression2test.yanxuan-static-service.service.mailsaas/yanxuan-static-service',
    online: 'http://127.0.0.1:8550/proxy/online.yanxuan-static-service.service.mailsaas/yanxuan-static-service'
  }

  getUasServiceUrl() {
    return getServiceUrl(this.uasServices);
  }

  getMsgServiceUrl() {
    return getServiceUrl(this.msgServices);
  }

  getStaticServiceUrl() {
    return getServiceUrl(this.staticServices);
  }
}