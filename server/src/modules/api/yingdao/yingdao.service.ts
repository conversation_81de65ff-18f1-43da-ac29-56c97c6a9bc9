import { Service } from '@tiger/boot';
import { appLogger } from '@tiger/logger';
import axios from '@tiger/request/publish/src/lib/http';
import * as config from '../../../conf';
import { JobStartParams, ParamType, TaskStartParams, YingdaoAppParams } from './yingdao.type';
import { RobotListConfig } from '../../../apolloy';

@Service
export class YingdaoService {
  token: string

  expiresTime: number

  getAccessToken(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        if (this.token && Date.now() < this.expiresTime) {
          resolve(this.token)
          return
        }
        const res = await axios.post(
          `https://api.yingdao.com/oapi/token/v2/token/create?accessKeyId=${config.accessKeyId}&accessKeySecret=${config.accessKeySecret}`
        )
        if (res.data.code == 200) {
          this.token = res.data.data.accessToken
          this.expiresTime = Date.now() + res.data.data.expiresIn * 1000
          resolve(this.token)
        } else {
          appLogger.error(`获取accessToken失败:${res.data.errorCode}`)
          resolve(null)
        }
      } catch (e) {
        appLogger.error(`获取accessToken失败:${e}`)
        reject(`获取accessToken失败:${e}`)
      }
    })
  }

  tiggerTask(params: TaskStartParams): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        const accessToken = await this.getAccessToken()
        const res = await axios.post(
          `https://api.yingdao.com/oapi/dispatch/v2/task/start`,
          params,
          { headers: { Authorization: `Bearer ${accessToken}` } }
        )
        if (res.data.code == 200) {
          resolve(res.data.data)
        } else {
          appLogger.error(`启动影刀任务失败:${res.data.message}`)
          reject(res.data)
        }
      } catch (e) {
        appLogger.error(`启动影刀任务失败:${e}`)
        reject(e)
      }
    })
  }

  tiggerJob(params: JobStartParams): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        const accessToken = await this.getAccessToken()
        const res = await axios.post(
          `https://api.yingdao.com/oapi/dispatch/v2/job/start`,
          params,
          { headers: { Authorization: `Bearer ${accessToken}` } }
        )
        if (res.data.code == 200) {
          resolve(res.data.data)
        } else {
          appLogger.error(`启动影刀应用失败:${res.data.message}`)
          reject(res.data)
        }
      } catch (e) {
        appLogger.error(`启动影刀应用失败:${e}`)
        reject(e)
      }
    })
  }

  formatAppParams(params: {[key: string]: any}) {
    const formatted: YingdaoAppParams = []
    // 遍历params所有键值对
    for (const key in params) {
      let value = params[key]
      let paramType: ParamType
      switch (typeof value) {
        case 'boolean':
          paramType = ParamType.BOOLEAN
          break
        case 'number':
          paramType = ParamType.INTEGER
          break
        case 'string':
          paramType = ParamType.STRING
          break
        case 'object':
          paramType = ParamType.STRING
          value = JSON.stringify(value)
          break
        default:
          paramType = ParamType.STRING
          break
      }

      formatted.push({
        name: key,
        value: value,
        type: paramType,
      })
    }
    return formatted
  }

  getCommonParams(name: string, path: string) {
    let apolloValue
    switch (name) {
      case 'externalFlowAppConfig':
        apolloValue = RobotListConfig.externalFlowAppConfig
        break
      case 'channelAppIdMap':
        apolloValue = RobotListConfig.channelAppIdMap
      case 'channelRobotMap':
        apolloValue = RobotListConfig.channelRobotMap
      default:
        apolloValue = ''
        break 
    }

    if (!apolloValue) {
      throw new Error(`未找到配置:${name}`)
    }
    let value = JSON.parse(apolloValue)
    const _path = path.split('.')
    for (const p of _path) {
      value = value[p]
      if (!value) {
        break
      }
    }

    if (!value) {
      throw new Error(`未找到配置:${name}.${path}`)
    }
    return JSON.parse(value)
  }
}
