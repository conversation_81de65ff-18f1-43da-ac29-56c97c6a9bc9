import { PostMapping, RequestMapping, RestController } from '@tiger/boot'
import { AjaxResult, RequestContext } from '@tiger/core'
import { appLogger } from '@tiger/logger'
import serviceAuth from '../../../middlewares/serviceAuth.middleware'
import { YingdaoService } from './yingdao.service';
import { JobStartVO, TaskStartVO } from './vo/yingdaoStart.vo';
import { JobStartResultVO, TaskStartResultVO } from './vo/yingdaoStartResult.vo';

@RestController
@RequestMapping('', [serviceAuth])
export class YingdaoController {
  constructor(
    private yingdaoService: YingdaoService
  ) {}

  /**
   * 触发RPA任务（仅支持任务内包含一个应用）
   */
  @PostMapping('/task/start')
  async trigger(ctx: RequestContext<TaskStartVO, AjaxResult<TaskStartResultVO>>) {
    const { body } = ctx.request;
    if (!body || !body.configName || !body.configPath) {
      appLogger.error(`参数错误${JSON.stringify(body)}`);
      ctx.body = AjaxResult.badRequest('参数错误');
      return;
    }

    const { configName, configPath, appParams } = body
    const commomParams = this.yingdaoService.getCommonParams(configName, configPath)
    if (!commomParams) {
      appLogger.error(`获取通用参数异常！${JSON.stringify(body)}`)
      ctx.body = AjaxResult.badRequest('获取通用参数异常');
      return;
    }
    const params = {
      scheduleUuid: commomParams.scheduleUuid,
      idempotentUuid: commomParams.idempotentUuid,
      scheduleRelaParams: [
        {
          robotUuid: commomParams.robotUuid,
          params: this.yingdaoService.formatAppParams(appParams),
        },
      ],
    }
    const result = await this.yingdaoService.tiggerTask(params)
    appLogger.info(`触发RPA任务成功：${JSON.stringify(result)}`);
    ctx.body = AjaxResult.success(result);
  }

  /**
   * 触发RPA应用
   */
  @PostMapping('/job/start')
  async triggerJob(ctx: RequestContext<JobStartVO, AjaxResult<JobStartResultVO>>) {
    const { body } = ctx.request;
    if (!body || !body.configName || !body.configPath) {
      appLogger.error(`参数错误${JSON.stringify(body)}`)
      ctx.body = AjaxResult.badRequest('参数错误')
      return
    }

    const { configName, configPath, appParams } = body
    const commonParams = this.yingdaoService.getCommonParams(configName, configPath)
    if (!commonParams) {
      appLogger.error(`获取通用参数异常！${JSON.stringify(body)}`)
      ctx.body = AjaxResult.badRequest('获取通用参数异常')
      return
    }
    const params = {
      ...commonParams,
      params: this.yingdaoService.formatAppParams(appParams),
    }
    const result = await this.yingdaoService.tiggerJob(params)
    appLogger.info(`触发RPA应用成功：${JSON.stringify(result)}`)
    ctx.body = AjaxResult.success(result)
  }
}
