export interface JobStartParams {
  accountName?: string // 机器人账号名称
  robotClientGroupUuid?: string // 机器人分组名称
  robotUuid: string // 应用uuid
  idempotentUuid?: string // 幂等uuid
  waitTimeout?: WaitTimeout // 等待超时时间 枚举类型
  waitTimeoutSeconds?: number // 等待超时时间 单位秒
  runTimeout?: number // 应用运行超时
  priority?: Priority // 优先级
  executeScope?: ExecuteScope // 执行范围
  params?: YingdaoAppParams // 应用参数
}

export interface TaskStartParams {
  scheduleUuid: string // 任务uuid
  idempotentUuid?: string // 幂等uuid
  scheduleRelaParams?: {
    robotUuid?: string // 应用uuid
    runTimeout?: number // 应用运行超时
    params?: YingdaoAppParams
  }[]
}

export type YingdaoAppParams = {
  name: string // 参数名称
  value: string // 参数值
  type: ParamType // 参数类型
}[]

export enum WaitTimeout {
  '10m' = '10m',
  '20m' = '20m',
  '30m' = '30m',
  '1h' = '1h',
  '2h' = '2h'
}

export enum Priority {
  HIGH = 'high',
  MIDDLE = 'middle',
  LOW = 'low'
}

export enum ParamType {
  STRING = 'str',
  INTEGER = 'int',
  FLOAT = 'float',
  BOOLEAN = 'bool',
  FILE = 'file'
}

export enum ExecuteScope {
  ANY = 'any',
  ALL = 'all'
}