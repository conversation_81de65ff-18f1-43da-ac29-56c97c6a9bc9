import {
  PostMapping,
  RequestMapping,
  RestController
} from "@tiger/boot";

import { AjaxResult, RequestContext } from "@tiger/core";
import { appLogger } from "@tiger/logger";
import { OpenCodeService } from '../../xhr/code/open/openCode.service';
import serviceAuth from '../../../middlewares/serviceAuth.middleware';
import { GetCodeApiVO } from './vo/getCodeApi.vo';

@RestController
@RequestMapping("/open", [serviceAuth])
export class ApiOpenCodeController {
  constructor(
    private openCodeService: OpenCodeService,
  ) { }

  /**
   * 获取自动触发的验证码
   */
  @PostMapping('/getCodeByTriggerTime', [])
  async getCodeByTriggerTime(
    ctx: RequestContext<GetCodeApiVO, AjaxResult<string>>
  ) {
    const { body } = ctx.request;
    if (!body || !body.phone || !body.channel || !body.triggerTime) {
      appLogger.error(`参数错误${JSON.stringify(body)}`);
      ctx.body = AjaxResult.badRequest('参数错误');
      return;
    }
    const result = await this.openCodeService.getCodeByTiggerTime(body.phone, body.channel, body.triggerTime);
    appLogger.info(`获取验证码：${JSON.stringify({ ...body, result })}`)
    ctx.body = AjaxResult.success(result);
  }
}
