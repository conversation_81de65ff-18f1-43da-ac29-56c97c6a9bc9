import {IAppModuleConfig} from '@tiger/core';
import {SwaggerConfig} from '@tiger/swagger';
import IORedis, { RedisOptions, ClusterNode, ClusterOptions } from 'ioredis';

export interface AppModuleConfig {
    '@tiger/security': IAppModuleConfig<{
        csrf: boolean;
        'Strict-Transport-Security': boolean;
        'X-Frame-Options': boolean;
    }>;
    '@tiger/swagger': IAppModuleConfig<SwaggerConfig>;
    [key: string]: IAppModuleConfig;
}

declare type clusterConfig = {
  cluster?: boolean
  nodes?: ClusterNode[]
} & ClusterOptions
export type RedisConfigInterface = RedisOptions & clusterConfig

export type RedisClient = IORedis.Redis | IORedis.Cluster
