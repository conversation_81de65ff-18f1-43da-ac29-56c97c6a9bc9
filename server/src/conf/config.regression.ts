/*
 * @Date: 2024-01-03 14:37:43
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-27 11:34:54
 * @FilePath: /yanxuan-rpa-common-server/server/src/conf/config.regression.ts
 * @Description: 
 * @Author: Lamwolf<PERSON> <EMAIL>
 */
import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';
import { AppModuleConfig } from './types';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
  loggerPath: string = join('/home/<USER>/', this.serviceCode);
  // app自己的转发配置，需要开发自己改成自己应用的 TODO
  appProxyOptions: ITigerProxyOption = {
    target:
      'http://127.0.0.1:8550/proxy/regression.yanxuan-app.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  msgProxyOptions: ITigerProxyOption = {
    target:
      'http://127.0.0.1:8550/proxy/regression2test.yanxuan-msg.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  uasProxyOptions: ITigerProxyOption = {
    target:
      'http://127.0.0.1:8550/proxy/regression.logistics-uas.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };
  nosUploadProxyOptions: ITigerProxyOption = {
    target:
      'rpa.test.you.163.com',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path;
    }
  };

  umcProxyOptions: ITigerProxyOption = {
    target:
      'http://127.0.0.1:8550/proxy/regression.yanxuan-ius.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
        ),
        ''
      );
    }
  };
  popProxyOptions: ITigerProxyOption = {
    target:
      'http://127.0.0.1:8550/proxy/regression.yanxuan-pop-core.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen`
        ),
        ''
      );
    }
  };
  distributionOptions: ITigerProxyOption = {
    target:
      'http://127.0.0.1:8550/proxy/regression.yanxuan-distribution-ic-app.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen`
        ),
        ''
      );
    }
  };
  videoProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.short-video-ops.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/video`
        ),
        ''
      );
    }
  }
  pddProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-pop-pddcommon.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/pdd`
        ),
        ''
      );
    }
  }
  
  kfadminOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-kfadmin.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/kfadmin`
        ),
        ''
      );
    }
  }
    
  nlpProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-nlp-server.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/nlp`
        ),
        ''
      );
    }
  }
  rpaBusinessProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-rpa-business-server.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/business`
        ),
        ''
      );
    }
  }
  yanxuanOrderQueryProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-order-query-server.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/yanxuanOrderQuery`
        ),
        ''
      );
    }
  }
  yanxuanStaticServiceProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-static-service-server.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/yanxuanStatic`
        ),
        ''
      );
    }
  }

  commentServiceProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/test.sylas.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/comment`
        ),
        ''
      );
    }
  }

  svProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yx-sv-center-admin.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}(/rpaOpen)?/sv`
        ),
        ''
      );
    }
  }

  iusProxyOptions: ITigerProxyOption = {
    target: 'http://127.0.0.1:8550/proxy/regression.yanxuan-ius.service.mailsaas',
    changeOrigin: true,
    autoRewrite: true,
    headers: {},
    rewrite: (path: string) => {
      return path.replace(
        new RegExp(
          `^${this.contextPath}${this.xhrPrefix}/rpaOpen/ius`
        ),
        ''
      );
    }
  }
  
  // Redis配置
  redisConfig = {
    host: 'redis.regression.you.163.com',
    port: 6379,
    db: 0
  };

  modules: AppModuleConfig = {
    '@tiger/security': {
      enable: true,
      options: {
        csrf: true,
        'Strict-Transport-Security': true,
        'X-Frame-Options': true
      }
    },
    '@tiger/swagger': {
      enable: false
    }
  };
}
