/*
 * @Date: 2024-01-03 17:49:22
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-26 14:49:57
 * @FilePath: /yanxuan-rpa-common-server/server/src/util/util.ts
 * @Description: 工具方法集合
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 */

import Cryptojs from 'crypto-js'

export function getErrorMessage(e: unknown): string {
  let finalError: string = '';
  if (typeof e === 'string') {
    finalError = e;
  } else if (e instanceof Error) {
    finalError = e.message;
  } else {
    finalError = '';
  }
  return finalError;
}

export function encryptResponse(data: any, secret: string, iv: string): string {
  if (!data || !secret) {
    return ''
  }
  const key = Cryptojs.enc.Hex.parse(secret);
  const ivHex = Cryptojs.enc.Hex.parse(iv);
  const encryptedString = Cryptojs.AES.encrypt(JSON.stringify(data), key, {
    iv: ivHex,
    mode: Cryptojs.mode.CBC,
    padding: Cryptojs.pad.Pkcs7,
  }).toString();
  return encryptedString;
}

// 格式化日期
export function formatDate(dateTime: string | number, format = 'yyyy-MM-dd HH:mm:ss'): string {
  const date = new Date(dateTime);
  const o: any = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
    }
  }
  return format;
}

/**
 * @description: 通过属性进行对象的匹配，并且sourceObj必须能够匹配上targetObj中的所有属性字段值，匹配成功返回true，否则返回false
 * @param {any} targetObj 目标要获取的对象
 * @param {any} sourceObj 源对象
 * @returns {}
 */
export function matchByAllProperties(targetObj: any, sourceObj: any): boolean {
  for (const key in targetObj) {
    if (targetObj[key] === null || targetObj[key] === undefined || targetObj[key] === '') {
      continue;
    }
    if (!sourceObj[key] || sourceObj[key] !== targetObj[key]) {
      return false;
    }
  }
  return true;
}