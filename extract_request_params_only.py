#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只提取 requestParams 的值，不包含其他元数据
"""

import json
import re
from datetime import datetime

def extract_request_params_values_only(log_file_path: str) -> list:
    """
    从日志文件中只提取 requestParams 的值
    
    Args:
        log_file_path: 日志文件路径
        
    Returns:
        只包含 requestParams 值的列表
    """
    request_params_list = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if 'requestParams' in line:
                    try:
                        # 提取 requestParams 的值
                        params_start = line.find('requestParams=')
                        if params_start != -1:
                            params_start += len('requestParams=')
                            params_str = line[params_start:].strip()
                            
                            # 如果 requestParams 后面还有其他字段，需要找到 JSON 的结束位置
                            if params_str.startswith('{'):
                                # 找到匹配的右括号，考虑字符串中的转义字符
                                bracket_count = 0
                                json_end = 0
                                in_string = False
                                escape_next = False
                                
                                for i, char in enumerate(params_str):
                                    if escape_next:
                                        escape_next = False
                                        continue
                                        
                                    if char == '\\':
                                        escape_next = True
                                        continue
                                        
                                    if char == '"' and not escape_next:
                                        in_string = not in_string
                                        continue
                                        
                                    if not in_string:
                                        if char == '{':
                                            bracket_count += 1
                                        elif char == '}':
                                            bracket_count -= 1
                                            if bracket_count == 0:
                                                json_end = i + 1
                                                break
                                
                                if json_end > 0:
                                    params_str = params_str[:json_end]
                            
                            # 解析 JSON
                            request_params = json.loads(params_str)
                            request_params_list.append(request_params)
                            print(f"✓ 成功提取第 {line_num} 行的 requestParams")
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠ 第 {line_num} 行 JSON 解析失败: {e}")
                        continue
                    except Exception as e:
                        print(f"✗ 第 {line_num} 行处理失败: {e}")
                        continue
                        
    except FileNotFoundError:
        print(f"✗ 文件未找到: {log_file_path}")
        return []
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return []
    
    return request_params_list

def save_request_params_only(data: list, output_file: str):
    """
    将提取的 requestParams 值保存到 JSON 文件
    
    Args:
        data: requestParams 值的列表
        output_file: 输出文件路径
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 数据已保存到: {output_file}")
        print(f"✓ 总共提取了 {len(data)} 条 requestParams 记录")
        
    except Exception as e:
        print(f"✗ 保存文件失败: {e}")

def main():
    """主函数"""
    log_file_path = "/Users/<USER>/Desktop/app.log.2025-07-09"
    output_file = "extracted_request_params_2025-07-09.json"
    
    print("开始提取 requestParams 值...")
    print(f"源文件: {log_file_path}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    # 提取数据
    request_params_list = extract_request_params_values_only(log_file_path)
    
    if request_params_list:
        # 保存数据
        save_request_params_only(request_params_list, output_file)
        
        # 显示统计信息
        print("-" * 50)
        print("提取完成！统计信息:")
        print(f"- 总记录数: {len(request_params_list)}")
        
        # 显示第一条记录的基本信息作为示例
        if request_params_list:
            first_record = request_params_list[0]
            print(f"- 第一条记录包含的字段: {list(first_record.keys())}")
            if 'skuList' in first_record:
                print(f"- 第一条记录的 SKU 数量: {len(first_record['skuList'])}")
            
    else:
        print("未找到任何包含 requestParams 的日志条目")

if __name__ == "__main__":
    main()
