#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成 requestParams 提取结果的摘要报告
"""

import json
from datetime import datetime

def generate_summary_report():
    """生成摘要报告"""
    
    # 读取提取的数据
    with open('extracted_request_params_2025-07-09.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    extraction_info = data['extraction_info']
    extracted_data = data['extracted_request_params']
    
    print("=" * 80)
    print("日志文件 requestParams 提取结果摘要报告")
    print("=" * 80)
    print(f"源文件: {extraction_info['source_file']}")
    print(f"提取时间: {extraction_info['extraction_time']}")
    print(f"总记录数: {extraction_info['total_entries']}")
    print()
    
    # 统计信息
    successful_parses = sum(1 for item in extracted_data if 'request_params' in item)
    failed_parses = len(extracted_data) - successful_parses
    
    print("统计信息:")
    print(f"- 成功解析的 JSON 记录: {successful_parses}")
    print(f"- 解析失败的记录: {failed_parses}")
    print()
    
    # 时间范围
    timestamps = [item['timestamp'] for item in extracted_data if item['timestamp']]
    if timestamps:
        print(f"时间范围: {min(timestamps)} ~ {max(timestamps)}")
        print()
    
    # 显示每条记录的基本信息
    print("提取的记录详情:")
    print("-" * 80)
    
    for i, item in enumerate(extracted_data, 1):
        print(f"记录 {i}:")
        print(f"  行号: {item['line_number']}")
        print(f"  时间戳: {item['timestamp']}")
        print(f"  跟踪ID: {item['trace_id']}")
        print(f"  日志级别: {item['level']}")
        
        if 'request_params' in item:
            params = item['request_params']
            print(f"  解析状态: ✓ 成功")
            
            # 显示 requestParams 的主要字段
            if 'skuList' in params:
                sku_count = len(params['skuList'])
                print(f"  SKU 数量: {sku_count}")
                
                # 显示第一个 SKU 的基本信息
                if sku_count > 0:
                    first_sku = params['skuList'][0]
                    print(f"  第一个商品:")
                    print(f"    平台: {first_sku.get('platformCode', 'N/A')}")
                    print(f"    商品ID: {first_sku.get('platformSpuId', 'N/A')}")
                    print(f"    价格: {first_sku.get('price1', 'N/A')}")
                    print(f"    商品名称: {first_sku.get('spuName', 'N/A')[:50]}...")
                    
                    # 评论数量
                    if 'commentList' in first_sku:
                        try:
                            comment_data = json.loads(first_sku['commentList'])
                            comment_count = len(comment_data) if isinstance(comment_data, list) else 0
                            print(f"    评论数量: {comment_count}")
                        except:
                            print(f"    评论数量: 无法解析")
        else:
            print(f"  解析状态: ✗ 失败")
            if 'parse_error' in item:
                print(f"  错误信息: {item['parse_error']}")
        
        print()

if __name__ == "__main__":
    generate_summary_report()
