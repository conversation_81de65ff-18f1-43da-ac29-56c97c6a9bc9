#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志文件 requestParams 提取脚本
从日志文件中提取所有包含 requestParams 字段的条目，并解析为结构化数据
"""

import json
import re
import sys
from datetime import datetime
from typing import List, Dict, Any

def extract_request_params_from_log(log_file_path: str) -> List[Dict[str, Any]]:
    """
    从日志文件中提取所有包含 requestParams 的条目
    
    Args:
        log_file_path: 日志文件路径
        
    Returns:
        包含所有提取的 requestParams 数据的列表
    """
    extracted_data = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if 'requestParams' in line:
                    try:
                        # 提取时间戳
                        time_match = re.search(r'time=([^,]+)', line)
                        timestamp = time_match.group(1) if time_match else None
                        
                        # 提取 traceId
                        trace_match = re.search(r'traceId=([^,]*)', line)
                        trace_id = trace_match.group(1) if trace_match else None
                        
                        # 提取 level
                        level_match = re.search(r'level=([^}]+)', line)
                        level = level_match.group(1) if level_match else None
                        
                        # 提取 requestParams 的值 - 改进的方法
                        params_start = line.find('requestParams=')
                        if params_start != -1:
                            params_start += len('requestParams=')
                            params_str = line[params_start:].strip()

                            # 尝试解析 JSON
                            try:
                                # 如果 requestParams 后面还有其他字段，需要找到 JSON 的结束位置
                                if params_str.startswith('{'):
                                    # 找到匹配的右括号，考虑字符串中的转义字符
                                    bracket_count = 0
                                    json_end = 0
                                    in_string = False
                                    escape_next = False

                                    for i, char in enumerate(params_str):
                                        if escape_next:
                                            escape_next = False
                                            continue

                                        if char == '\\':
                                            escape_next = True
                                            continue

                                        if char == '"' and not escape_next:
                                            in_string = not in_string
                                            continue

                                        if not in_string:
                                            if char == '{':
                                                bracket_count += 1
                                            elif char == '}':
                                                bracket_count -= 1
                                                if bracket_count == 0:
                                                    json_end = i + 1
                                                    break

                                    if json_end > 0:
                                        params_str = params_str[:json_end]

                                # 解析 JSON
                                request_params = json.loads(params_str)

                                # 构建提取的数据项
                                extracted_item = {
                                    "line_number": line_num,
                                    "timestamp": timestamp,
                                    "trace_id": trace_id,
                                    "level": level,
                                    "original_line": line,
                                    "request_params": request_params
                                }

                                extracted_data.append(extracted_item)
                                print(f"✓ 成功提取第 {line_num} 行的 requestParams")

                            except json.JSONDecodeError as e:
                                print(f"⚠ 第 {line_num} 行 JSON 解析失败: {e}")
                                print(f"   原始数据: {params_str[:100]}...")

                                # 即使 JSON 解析失败，也保存原始数据
                                extracted_item = {
                                    "line_number": line_num,
                                    "timestamp": timestamp,
                                    "trace_id": trace_id,
                                    "level": level,
                                    "original_line": line,
                                    "request_params_raw": params_str,
                                    "parse_error": str(e)
                                }
                                extracted_data.append(extracted_item)
                        else:
                            print(f"⚠ 第 {line_num} 行未能提取到 requestParams 值")
                            
                    except Exception as e:
                        print(f"✗ 第 {line_num} 行处理失败: {e}")
                        continue
                        
    except FileNotFoundError:
        print(f"✗ 文件未找到: {log_file_path}")
        return []
    except Exception as e:
        print(f"✗ 读取文件失败: {e}")
        return []
    
    return extracted_data

def save_extracted_data(data: List[Dict[str, Any]], output_file: str):
    """
    将提取的数据保存到 JSON 文件
    
    Args:
        data: 提取的数据列表
        output_file: 输出文件路径
    """
    try:
        # 构建输出数据结构
        output_data = {
            "extraction_info": {
                "extraction_time": datetime.now().isoformat(),
                "total_entries": len(data),
                "source_file": "/Users/<USER>/Desktop/app.log.2025-07-09"
            },
            "extracted_request_params": data
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 数据已保存到: {output_file}")
        print(f"✓ 总共提取了 {len(data)} 条 requestParams 记录")
        
    except Exception as e:
        print(f"✗ 保存文件失败: {e}")

def main():
    """主函数"""
    log_file_path = "/Users/<USER>/Desktop/app.log.2025-07-09"
    output_file = "extracted_request_params_2025-07-09.json"
    
    print("开始提取 requestParams 数据...")
    print(f"源文件: {log_file_path}")
    print(f"输出文件: {output_file}")
    print("-" * 50)
    
    # 提取数据
    extracted_data = extract_request_params_from_log(log_file_path)
    
    if extracted_data:
        # 保存数据
        save_extracted_data(extracted_data, output_file)
        
        # 显示统计信息
        print("-" * 50)
        print("提取完成！统计信息:")
        print(f"- 总记录数: {len(extracted_data)}")
        
        # 统计成功解析的 JSON 数量
        successful_parses = sum(1 for item in extracted_data if 'request_params' in item)
        failed_parses = len(extracted_data) - successful_parses
        
        print(f"- 成功解析 JSON: {successful_parses}")
        print(f"- 解析失败: {failed_parses}")
        
        # 显示时间范围
        timestamps = [item['timestamp'] for item in extracted_data if item['timestamp']]
        if timestamps:
            print(f"- 时间范围: {min(timestamps)} ~ {max(timestamps)}")
            
    else:
        print("未找到任何包含 requestParams 的日志条目")

if __name__ == "__main__":
    main()
